import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logger } from '@shared/utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 记录错误
    logger.error('React Error Boundary caught an error:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo: {
        componentStack: errorInfo.componentStack
      }
    });

    // 在生产环境中发送错误报告
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 发送错误报告到监控服务
    if (window.electronAPI) {
      window.electronAPI.reportError({
        name: error.name,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleRestart = () => {
    if (window.electronAPI) {
      window.electronAPI.restartApp();
    }
  };

  render() {
    if (this.state.hasError) {
      // 自定义错误UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex flex-col items-center justify-center w-full h-full p-8 bg-white">
          <div className="max-w-md text-center">
            {/* 错误图标 */}
            <div className="text-6xl mb-6">😵</div>
            
            {/* 错误标题 */}
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              哎呀，出错了！
            </h1>
            
            {/* 错误描述 */}
            <p className="text-gray-600 mb-6">
              桌面宠物遇到了一个意外错误。不用担心，您的宠物数据是安全的。
            </p>

            {/* 错误详情（仅开发模式） */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 mb-2">
                  错误详情 (开发模式)
                </summary>
                <div className="bg-gray-100 p-4 rounded text-xs font-mono overflow-auto max-h-40">
                  <div className="text-red-600 font-bold mb-2">
                    {this.state.error.name}: {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <pre className="whitespace-pre-wrap text-gray-700">
                      {this.state.error.stack}
                    </pre>
                  )}
                  {this.state.errorInfo && (
                    <div className="mt-4">
                      <div className="text-blue-600 font-bold mb-2">组件堆栈:</div>
                      <pre className="whitespace-pre-wrap text-gray-700">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* 操作按钮 */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={this.handleReload}
                className="px-6 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors"
              >
                重新加载
              </button>
              
              <button
                onClick={this.handleRestart}
                className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                重启应用
              </button>
            </div>

            {/* 帮助信息 */}
            <div className="mt-8 text-sm text-gray-500">
              <p>如果问题持续存在，请尝试：</p>
              <ul className="mt-2 text-left list-disc list-inside space-y-1">
                <li>重启应用</li>
                <li>检查是否有应用更新</li>
                <li>联系技术支持</li>
              </ul>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
