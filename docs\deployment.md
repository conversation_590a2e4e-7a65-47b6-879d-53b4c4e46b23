# 部署指南

## 概述

本文档详细说明了桌面宠物应用的构建、打包和部署流程，包括开发环境部署、生产环境构建和多平台发布。

## 环境准备

### 开发环境要求

#### 系统要求
- **操作系统**：Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Node.js**：18.0 或更高版本
- **npm**：8.0 或更高版本（或 yarn 1.22+）
- **Git**：2.30 或更高版本

#### 开发工具
- **代码编辑器**：VS Code（推荐）
- **设计工具**：Figma, Sketch（可选）
- **图像处理**：Photoshop, GIMP（可选）

### 依赖安装

```bash
# 克隆项目
git clone https://github.com/your-username/desktop-pet.git
cd desktop-pet

# 安装依赖
npm install

# 安装开发工具
npm install -g electron-builder
```

## 开发环境部署

### 本地开发服务器

```bash
# 启动开发服务器
npm run dev

# 或分别启动
npm run dev:renderer  # 启动渲染进程开发服务器
npm run dev:main      # 启动主进程
```

### 开发环境配置

#### 环境变量配置
创建 `.env.local` 文件：

```bash
# 应用配置
VITE_APP_NAME=Desktop Pet
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=A cute desktop companion

# 开发配置
VITE_DEV_MODE=true
VITE_DEBUG_MODE=true
VITE_HOT_RELOAD=true

# API 配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=5000

# 功能开关
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CRASH_REPORTING=false
```

#### 开发者工具配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

## 构建流程

### 开发构建

```bash
# 构建开发版本
npm run build:dev

# 构建并启动
npm run build:dev && npm run start
```

### 生产构建

```bash
# 构建生产版本
npm run build

# 清理并构建
npm run clean && npm run build

# 构建并分析包大小
npm run build:analyze
```

### 构建配置

#### Vite 配置 (vite.config.ts)
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist/renderer',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/renderer/index.html')
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer'),
      '@shared': resolve(__dirname, 'src/shared')
    }
  }
});
```

#### Electron Builder 配置 (electron-builder.json)
```json
{
  "appId": "com.example.desktop-pet",
  "productName": "Desktop Pet",
  "directories": {
    "output": "release"
  },
  "files": [
    "dist/**/*",
    "assets/**/*",
    "package.json"
  ],
  "extraResources": [
    {
      "from": "assets/",
      "to": "assets/"
    }
  ],
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64", "ia32"]
      },
      {
        "target": "portable",
        "arch": ["x64"]
      }
    ],
    "icon": "assets/icons/icon.ico"
  },
  "mac": {
    "target": [
      {
        "target": "dmg",
        "arch": ["x64", "arm64"]
      }
    ],
    "icon": "assets/icons/icon.icns",
    "category": "public.app-category.entertainment"
  },
  "linux": {
    "target": [
      {
        "target": "AppImage",
        "arch": ["x64"]
      },
      {
        "target": "deb",
        "arch": ["x64"]
      }
    ],
    "icon": "assets/icons/icon.png",
    "category": "Game"
  }
}
```

## 应用打包

### 单平台打包

```bash
# Windows 平台
npm run pack:win

# macOS 平台
npm run pack:mac

# Linux 平台
npm run pack:linux
```

### 多平台打包

```bash
# 打包所有平台
npm run pack:all

# 仅打包当前平台
npm run pack
```

### 打包脚本配置

```json
// package.json
{
  "scripts": {
    "pack": "electron-builder",
    "pack:win": "electron-builder --win",
    "pack:mac": "electron-builder --mac",
    "pack:linux": "electron-builder --linux",
    "pack:all": "electron-builder --win --mac --linux",
    "dist": "npm run build && npm run pack:all"
  }
}
```

## 发布流程

### 版本管理

#### 版本号规范
遵循语义化版本控制 (SemVer)：
- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

```bash
# 更新版本号
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.1 -> 1.1.0
npm version major  # 1.1.0 -> 2.0.0
```

#### 发布准备
```bash
# 1. 更新版本号
npm version minor

# 2. 更新 CHANGELOG
npm run changelog

# 3. 构建和测试
npm run build
npm run test

# 4. 打包应用
npm run pack:all

# 5. 提交更改
git add .
git commit -m "Release v1.1.0"
git tag v1.1.0
git push origin main --tags
```

### 自动化发布

#### GitHub Actions 配置
```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
      
      - name: Package application
        run: npm run pack
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: release-${{ matrix.os }}
          path: release/
```

### 手动发布

#### 1. 准备发布文件
```bash
# 创建发布目录
mkdir -p release/v1.1.0

# 复制安装包
cp release/*.exe release/v1.1.0/  # Windows
cp release/*.dmg release/v1.1.0/  # macOS
cp release/*.AppImage release/v1.1.0/  # Linux
```

#### 2. 创建发布说明
```markdown
# Desktop Pet v1.1.0

## 新功能
- 添加了新的宠物类型：小鸟
- 支持自定义宠物名称
- 新增成就系统

## 改进
- 优化了动画性能
- 改进了用户界面
- 增强了稳定性

## 修复
- 修复了宠物偶尔消失的问题
- 修复了设置保存失败的问题
- 修复了内存泄漏问题

## 下载
- [Windows x64](./Desktop-Pet-Setup-1.1.0.exe)
- [Windows x32](./Desktop-Pet-Setup-1.1.0-ia32.exe)
- [macOS](./Desktop-Pet-1.1.0.dmg)
- [Linux AppImage](./Desktop-Pet-1.1.0.AppImage)
```

## 部署监控

### 错误监控

#### Sentry 集成
```typescript
// src/shared/monitoring.ts
import * as Sentry from '@sentry/electron';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  beforeSend(event) {
    // 过滤敏感信息
    return event;
  }
});
```

#### 自定义错误报告
```typescript
// src/shared/error-reporter.ts
export class ErrorReporter {
  static report(error: Error, context?: any) {
    if (process.env.NODE_ENV === 'production') {
      // 发送到错误监控服务
      Sentry.captureException(error, { extra: context });
    } else {
      console.error('Error:', error, context);
    }
  }
}
```

### 使用统计

#### 基础统计收集
```typescript
// src/shared/analytics.ts
export class Analytics {
  static track(event: string, properties?: any) {
    if (process.env.VITE_ENABLE_ANALYTICS === 'true') {
      // 发送统计数据
      this.sendEvent(event, properties);
    }
  }
  
  private static sendEvent(event: string, properties: any) {
    // 实现统计数据发送逻辑
  }
}
```

## 更新机制

### 自动更新配置

#### electron-updater 集成
```typescript
// src/main/updater.ts
import { autoUpdater } from 'electron-updater';

export class AppUpdater {
  constructor() {
    autoUpdater.checkForUpdatesAndNotify();
    
    autoUpdater.on('update-available', () => {
      // 通知用户有更新可用
    });
    
    autoUpdater.on('update-downloaded', () => {
      // 提示用户重启应用
    });
  }
}
```

#### 更新服务器配置
```json
// electron-builder.json
{
  "publish": [
    {
      "provider": "github",
      "owner": "your-username",
      "repo": "desktop-pet"
    }
  ]
}
```

## 性能优化

### 构建优化

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          animation: ['framer-motion'],
          utils: ['lodash', 'date-fns']
        }
      }
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

### 资源优化

```bash
# 图片压缩
npm install -g imagemin-cli
imagemin assets/images/* --out-dir=assets/images/optimized

# 音频压缩
ffmpeg -i input.wav -c:a libvorbis -q:a 4 output.ogg
```

## 故障排除

### 常见构建问题

#### 1. 依赖安装失败
```bash
# 清除缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 2. 打包失败
```bash
# 检查 electron-builder 配置
npx electron-builder --help

# 详细日志
DEBUG=electron-builder npm run pack
```

#### 3. 代码签名问题
```bash
# Windows 代码签名
set CSC_LINK=path/to/certificate.p12
set CSC_KEY_PASSWORD=your_password

# macOS 代码签名
export CSC_LINK=path/to/certificate.p12
export CSC_KEY_PASSWORD=your_password
```

### 调试技巧

```bash
# 启用详细日志
DEBUG=* npm run dev

# 检查构建产物
npm run build && ls -la dist/

# 测试打包结果
npm run pack && ls -la release/
```
