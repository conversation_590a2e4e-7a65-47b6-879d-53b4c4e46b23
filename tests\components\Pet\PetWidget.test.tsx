import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PetWidget } from '../../../src/renderer/components/Pet/PetWidget';
import { usePetStore } from '../../../src/renderer/stores/petStore';
import { useSettingsStore } from '../../../src/renderer/stores/settingsStore';

// Mock stores
jest.mock('../../../src/renderer/stores/petStore');
jest.mock('../../../src/renderer/stores/settingsStore');

const mockUsePetStore = usePetStore as jest.MockedFunction<typeof usePetStore>;
const mockUseSettingsStore = useSettingsStore as jest.MockedFunction<typeof useSettingsStore>;

describe('PetWidget', () => {
  const mockPet = {
    id: 'test-pet',
    name: '测试宠物',
    type: 'cat' as const,
    level: 1,
    experience: 50,
    stats: {
      hunger: 80,
      happiness: 90,
      health: 100,
      energy: 70,
      cleanliness: 85
    },
    position: { x: 100, y: 100 },
    size: 1.0,
    currentAnimation: 'idle' as const,
    mood: 'happy' as const,
    isVisible: true,
    lastFeedTime: Date.now(),
    lastPlayTime: Date.now(),
    lastCleanTime: Date.now(),
    birthTime: Date.now(),
    totalPlayTime: 0
  };

  const mockSettings = {
    showStats: true,
    showPetName: true,
    petSize: 1.0,
    enableSounds: true,
    soundVolume: 50,
    showStatusBar: false,
    theme: 'light' as const,
    enableNotifications: true,
    alwaysOnTop: true,
    autoStart: false,
    showInTaskbar: false,
    minimizeToTray: true,
    animationSpeed: 1.0,
    enableAutoBehavior: true,
    behaviorFrequency: 5,
    enableIdleMode: true,
    idleTimeout: 30,
    transparency: 0,
    notifyOnLowStats: true,
    notifyOnMilestones: true,
    enableAnalytics: false,
    enableCrashReporting: true,
    debugMode: false
  };

  const mockActions = {
    updatePetPosition: jest.fn(),
    feedPet: jest.fn(),
    playWithPet: jest.fn(),
    petPet: jest.fn(),
    updatePetAnimation: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUsePetStore.mockReturnValue({
      currentPet: mockPet,
      pets: [mockPet],
      isLoading: false,
      error: null,
      initializePet: jest.fn(),
      createPet: jest.fn(),
      selectPet: jest.fn(),
      deletePet: jest.fn(),
      updatePetStats: jest.fn(),
      updatePetMood: jest.fn(),
      cleanPet: jest.fn(),
      startAutoBehavior: jest.fn(),
      stopAutoBehavior: jest.fn(),
      savePetData: jest.fn(),
      loadPetData: jest.fn(),
      resetPetData: jest.fn(),
      ...mockActions
    });

    mockUseSettingsStore.mockReturnValue({
      settings: mockSettings,
      isLoading: false,
      error: null,
      loadSettings: jest.fn(),
      updateSettings: jest.fn(),
      resetSettings: jest.fn(),
      exportSettings: jest.fn(),
      importSettings: jest.fn(),
      setTheme: jest.fn(),
      setPetSize: jest.fn(),
      setVolume: jest.fn(),
      toggleAlwaysOnTop: jest.fn(),
      toggleSounds: jest.fn(),
      toggleNotifications: jest.fn()
    });
  });

  it('should render pet widget correctly', () => {
    render(<PetWidget />);
    
    // 检查宠物是否渲染
    expect(screen.getByText('🐱')).toBeInTheDocument();
    
    // 检查宠物名称是否显示
    expect(screen.getByText('测试宠物')).toBeInTheDocument();
    
    // 检查心情表情是否显示
    expect(screen.getByText('😊')).toBeInTheDocument();
  });

  it('should handle click interaction', async () => {
    render(<PetWidget />);
    
    const petElement = screen.getByText('🐱').closest('div');
    expect(petElement).toBeInTheDocument();
    
    if (petElement) {
      fireEvent.click(petElement);
      
      await waitFor(() => {
        expect(mockActions.petPet).toHaveBeenCalledTimes(1);
      });
    }
  });

  it('should handle double click interaction', async () => {
    render(<PetWidget />);
    
    const petElement = screen.getByText('🐱').closest('div');
    expect(petElement).toBeInTheDocument();
    
    if (petElement) {
      // 模拟双击
      fireEvent.click(petElement);
      fireEvent.click(petElement);
      
      await waitFor(() => {
        // 由于宠物饥饿值是80，应该触发喂食
        expect(mockActions.feedPet).toHaveBeenCalledTimes(1);
      });
    }
  });

  it('should handle context menu', () => {
    render(<PetWidget />);
    
    const petElement = screen.getByText('🐱').closest('div');
    expect(petElement).toBeInTheDocument();
    
    if (petElement) {
      fireEvent.contextMenu(petElement);
      // 右键菜单由App组件处理，这里只检查事件是否被阻止
      // 实际的右键菜单测试应该在App组件的测试中进行
    }
  });

  it('should display status indicator when showStats is enabled', () => {
    render(<PetWidget />);
    
    // 状态指示器应该显示
    const statusIndicator = document.querySelector('.absolute.-top-2.-right-2');
    expect(statusIndicator).toBeInTheDocument();
  });

  it('should not display status indicator when showStats is disabled', () => {
    mockUseSettingsStore.mockReturnValue({
      ...mockUseSettingsStore(),
      settings: {
        ...mockSettings,
        showStats: false
      }
    });

    render(<PetWidget />);
    
    // 状态指示器不应该显示
    const statusIndicator = document.querySelector('.absolute.-top-2.-right-2');
    expect(statusIndicator).not.toBeInTheDocument();
  });

  it('should not display pet name when showPetName is disabled', () => {
    mockUseSettingsStore.mockReturnValue({
      ...mockUseSettingsStore(),
      settings: {
        ...mockSettings,
        showPetName: false
      }
    });

    render(<PetWidget />);
    
    // 宠物名称不应该显示
    expect(screen.queryByText('测试宠物')).not.toBeInTheDocument();
  });

  it('should display different pet types correctly', () => {
    const petTypes = [
      { type: 'dog', emoji: '🐶' },
      { type: 'rabbit', emoji: '🐰' },
      { type: 'bird', emoji: '🐦' }
    ];

    petTypes.forEach(({ type, emoji }) => {
      mockUsePetStore.mockReturnValue({
        ...mockUsePetStore(),
        currentPet: {
          ...mockPet,
          type: type as any
        }
      });

      const { rerender } = render(<PetWidget />);
      expect(screen.getByText(emoji)).toBeInTheDocument();
      rerender(<div />); // 清理
    });
  });

  it('should display different moods correctly', () => {
    const moods = [
      { mood: 'sad', emoji: '😢' },
      { mood: 'angry', emoji: '😠' },
      { mood: 'sleepy', emoji: '😴' },
      { mood: 'excited', emoji: '🤩' },
      { mood: 'neutral', emoji: '😐' }
    ];

    moods.forEach(({ mood, emoji }) => {
      mockUsePetStore.mockReturnValue({
        ...mockUsePetStore(),
        currentPet: {
          ...mockPet,
          mood: mood as any
        }
      });

      const { rerender } = render(<PetWidget />);
      expect(screen.getByText(emoji)).toBeInTheDocument();
      rerender(<div />); // 清理
    });
  });

  it('should display animation effects for different actions', () => {
    const animations = [
      { animation: 'eat', icon: '🍎' },
      { animation: 'play', icon: '🎾' },
      { animation: 'clean', icon: '🛁' }
    ];

    animations.forEach(({ animation, icon }) => {
      mockUsePetStore.mockReturnValue({
        ...mockUsePetStore(),
        currentPet: {
          ...mockPet,
          currentAnimation: animation as any
        }
      });

      const { rerender } = render(<PetWidget />);
      expect(screen.getByText(icon)).toBeInTheDocument();
      rerender(<div />); // 清理
    });
  });

  it('should render fallback when no pet is available', () => {
    mockUsePetStore.mockReturnValue({
      ...mockUsePetStore(),
      currentPet: null
    });

    render(<PetWidget />);
    
    expect(screen.getByText('没有宠物')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const customClass = 'custom-pet-widget';
    const { container } = render(<PetWidget className={customClass} />);
    
    expect(container.firstChild).toHaveClass(customClass);
  });
});
