import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSettingsStore } from '../../stores/settingsStore';
import { clsx } from 'clsx';

interface SettingsModalProps {
  onClose: () => void;
}

type SettingsTab = 'general' | 'pet' | 'behavior' | 'appearance' | 'notifications' | 'advanced';

export const SettingsModal: React.FC<SettingsModalProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  const { settings, updateSettings, resetSettings } = useSettingsStore();

  const tabs = [
    { id: 'general' as const, label: '常规', icon: '⚙️' },
    { id: 'pet' as const, label: '宠物', icon: '🐱' },
    { id: 'behavior' as const, label: '行为', icon: '🎭' },
    { id: 'appearance' as const, label: '外观', icon: '🎨' },
    { id: 'notifications' as const, label: '通知', icon: '🔔' },
    { id: 'advanced' as const, label: '高级', icon: '🔧' },
  ];

  const handleClose = () => {
    onClose();
  };

  const handleReset = () => {
    if (confirm('确定要重置所有设置吗？此操作无法撤销。')) {
      resetSettings();
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">常规设置</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">开机自启</label>
                  <p className="text-xs text-gray-500">系统启动时自动运行桌面宠物</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.autoStart}
                  onChange={(e) => updateSettings({ autoStart: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">始终置顶</label>
                  <p className="text-xs text-gray-500">宠物窗口始终显示在最前面</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.alwaysOnTop}
                  onChange={(e) => updateSettings({ alwaysOnTop: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">最小化到托盘</label>
                  <p className="text-xs text-gray-500">关闭窗口时最小化到系统托盘</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.minimizeToTray}
                  onChange={(e) => updateSettings({ minimizeToTray: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        );

      case 'pet':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">宠物设置</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  宠物大小: {settings.petSize.toFixed(1)}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={settings.petSize}
                  onChange={(e) => updateSettings({ petSize: parseFloat(e.target.value) })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  动画速度: {settings.animationSpeed.toFixed(1)}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={settings.animationSpeed}
                  onChange={(e) => updateSettings({ animationSpeed: parseFloat(e.target.value) })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">显示宠物名称</label>
                  <p className="text-xs text-gray-500">在宠物下方显示名称</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.showPetName}
                  onChange={(e) => updateSettings({ showPetName: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用音效</label>
                  <p className="text-xs text-gray-500">播放宠物互动音效</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableSounds}
                  onChange={(e) => updateSettings({ enableSounds: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              {settings.enableSounds && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    音量: {settings.soundVolume}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={settings.soundVolume}
                    onChange={(e) => updateSettings({ soundVolume: parseInt(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              )}
            </div>
          </div>
        );

      case 'behavior':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">行为设置</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用自动行为</label>
                  <p className="text-xs text-gray-500">宠物会自动执行各种行为</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableAutoBehavior}
                  onChange={(e) => updateSettings({ enableAutoBehavior: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              {settings.enableAutoBehavior && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    行为频率: 每 {settings.behaviorFrequency} 分钟
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="30"
                    value={settings.behaviorFrequency}
                    onChange={(e) => updateSettings({ behaviorFrequency: parseInt(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用空闲模式</label>
                  <p className="text-xs text-gray-500">长时间无操作时进入睡眠状态</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableIdleMode}
                  onChange={(e) => updateSettings({ enableIdleMode: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              {settings.enableIdleMode && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    空闲超时: {settings.idleTimeout} 分钟
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="120"
                    value={settings.idleTimeout}
                    onChange={(e) => updateSettings({ idleTimeout: parseInt(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              )}
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">外观设置</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">主题</label>
                <select
                  value={settings.theme}
                  onChange={(e) => updateSettings({ theme: e.target.value as any })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="light">明亮</option>
                  <option value="dark">暗黑</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">显示状态栏</label>
                  <p className="text-xs text-gray-500">显示宠物状态信息</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.showStatusBar}
                  onChange={(e) => updateSettings({ showStatusBar: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">显示状态指示器</label>
                  <p className="text-xs text-gray-500">在宠物身上显示状态点</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.showStats}
                  onChange={(e) => updateSettings({ showStats: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  透明度: {settings.transparency}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="50"
                  value={settings.transparency}
                  onChange={(e) => updateSettings({ transparency: parseInt(e.target.value) })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">通知设置</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用通知</label>
                  <p className="text-xs text-gray-500">显示系统通知</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableNotifications}
                  onChange={(e) => updateSettings({ enableNotifications: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              {settings.enableNotifications && (
                <>
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">低状态提醒</label>
                      <p className="text-xs text-gray-500">宠物状态过低时提醒</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifyOnLowStats}
                      onChange={(e) => updateSettings({ notifyOnLowStats: e.target.checked })}
                      className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">成就通知</label>
                      <p className="text-xs text-gray-500">达成成就时通知</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifyOnMilestones}
                      onChange={(e) => updateSettings({ notifyOnMilestones: e.target.checked })}
                      className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        );

      case 'advanced':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-800">高级设置</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">启用分析</label>
                  <p className="text-xs text-gray-500">帮助改进应用体验</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableAnalytics}
                  onChange={(e) => updateSettings({ enableAnalytics: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">崩溃报告</label>
                  <p className="text-xs text-gray-500">自动发送崩溃报告</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.enableCrashReporting}
                  onChange={(e) => updateSettings({ enableCrashReporting: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">调试模式</label>
                  <p className="text-xs text-gray-500">显示调试信息</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.debugMode}
                  onChange={(e) => updateSettings({ debugMode: e.target.checked })}
                  className="w-4 h-4 text-primary-500 rounded focus:ring-primary-500"
                />
              </div>

              <div className="pt-4 border-t border-gray-200">
                <button
                  onClick={handleReset}
                  className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  重置所有设置
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={handleClose}
    >
      <motion.div
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">设置</h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <span className="text-xl">×</span>
          </button>
        </div>

        <div className="flex h-[500px]">
          {/* 侧边栏 */}
          <div className="w-48 bg-gray-50 border-r border-gray-200">
            <nav className="p-4 space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={clsx(
                    "w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors",
                    activeTab === tab.id
                      ? "bg-primary-100 text-primary-700"
                      : "text-gray-600 hover:bg-gray-100"
                  )}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {renderTabContent()}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};
