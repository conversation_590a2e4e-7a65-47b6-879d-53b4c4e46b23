import { Tray, Menu, app, nativeImage } from 'electron';
import { join } from 'path';
import { logger } from '@shared/utils/logger';

export class TrayManager {
  private tray: Tray | null = null;

  async initialize(): Promise<void> {
    try {
      await this.createTray();
      logger.info('Tray Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Tray Manager:', error);
      throw error;
    }
  }

  private async createTray(): Promise<void> {
    // 创建托盘图标
    const iconPath = this.getTrayIconPath();
    const trayIcon = nativeImage.createFromPath(iconPath);
    
    // 调整图标大小 (macOS 需要16x16, Windows 需要16x16或32x32)
    if (process.platform === 'darwin') {
      trayIcon.setTemplateImage(true); // macOS 模板图像
    }

    this.tray = new Tray(trayIcon);

    // 设置托盘提示文本
    this.tray.setToolTip('桌面宠物 - Desktop Pet');

    // 创建上下文菜单
    this.createContextMenu();

    // 托盘事件监听
    this.setupTrayEvents();
  }

  private getTrayIconPath(): string {
    const iconName = process.platform === 'win32' ? 'tray-icon.ico' : 
                     process.platform === 'darwin' ? 'tray-icon.png' : 
                     'tray-icon.png';
    
    return join(__dirname, '../../assets/icons', iconName);
  }

  private createContextMenu(): void {
    if (!this.tray) return;

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示宠物',
        type: 'normal',
        click: () => {
          this.emit('show-pet');
        }
      },
      {
        label: '隐藏宠物',
        type: 'normal',
        click: () => {
          this.emit('hide-pet');
        }
      },
      { type: 'separator' },
      {
        label: '宠物状态',
        type: 'submenu',
        submenu: [
          {
            label: '喂食',
            type: 'normal',
            click: () => {
              this.emit('feed-pet');
            }
          },
          {
            label: '玩耍',
            type: 'normal',
            click: () => {
              this.emit('play-with-pet');
            }
          },
          {
            label: '清洁',
            type: 'normal',
            click: () => {
              this.emit('clean-pet');
            }
          },
          { type: 'separator' },
          {
            label: '查看状态',
            type: 'normal',
            click: () => {
              this.emit('show-status');
            }
          }
        ]
      },
      {
        label: '宠物设置',
        type: 'submenu',
        submenu: [
          {
            label: '选择宠物',
            type: 'normal',
            click: () => {
              this.emit('select-pet');
            }
          },
          {
            label: '外观设置',
            type: 'normal',
            click: () => {
              this.emit('appearance-settings');
            }
          },
          {
            label: '行为设置',
            type: 'normal',
            click: () => {
              this.emit('behavior-settings');
            }
          }
        ]
      },
      { type: 'separator' },
      {
        label: '设置',
        type: 'normal',
        click: () => {
          this.emit('open-settings');
        }
      },
      {
        label: '关于',
        type: 'normal',
        click: () => {
          this.emit('show-about');
        }
      },
      { type: 'separator' },
      {
        label: '重启应用',
        type: 'normal',
        click: () => {
          this.emit('restart-app');
        }
      },
      {
        label: '退出',
        type: 'normal',
        click: () => {
          this.emit('quit-app');
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
  }

  private setupTrayEvents(): void {
    if (!this.tray) return;

    // 左键点击事件
    this.tray.on('click', () => {
      this.emit('tray-click');
    });

    // 双击事件
    this.tray.on('double-click', () => {
      this.emit('tray-double-click');
    });

    // 右键点击事件 (Windows)
    this.tray.on('right-click', () => {
      this.emit('tray-right-click');
    });

    // 气球提示点击事件 (Windows)
    this.tray.on('balloon-click', () => {
      this.emit('balloon-click');
    });
  }

  updateTrayIcon(iconType: 'normal' | 'notification' | 'sleeping' | 'happy' | 'sad'): void {
    if (!this.tray) return;

    try {
      const iconMap = {
        normal: 'tray-icon.png',
        notification: 'tray-icon-notification.png',
        sleeping: 'tray-icon-sleeping.png',
        happy: 'tray-icon-happy.png',
        sad: 'tray-icon-sad.png'
      };

      const iconName = iconMap[iconType] || iconMap.normal;
      const iconPath = join(__dirname, '../../assets/icons', iconName);
      const newIcon = nativeImage.createFromPath(iconPath);

      if (process.platform === 'darwin') {
        newIcon.setTemplateImage(true);
      }

      this.tray.setImage(newIcon);
    } catch (error) {
      logger.error('Failed to update tray icon:', error);
    }
  }

  showBalloon(title: string, content: string, iconType: 'info' | 'warning' | 'error' = 'info'): void {
    if (!this.tray || process.platform !== 'win32') return;

    try {
      this.tray.displayBalloon({
        title,
        content,
        iconType,
        largeIcon: false,
        noSound: false,
        respectQuietTime: true
      });
    } catch (error) {
      logger.error('Failed to show balloon notification:', error);
    }
  }

  updateTooltip(text: string): void {
    if (!this.tray) return;
    this.tray.setToolTip(text);
  }

  updateContextMenu(petStatus?: {
    hunger: number;
    happiness: number;
    health: number;
    energy: number;
  }): void {
    if (!this.tray) return;

    const statusText = petStatus ? 
      `饥饿: ${petStatus.hunger}% | 快乐: ${petStatus.happiness}% | 健康: ${petStatus.health}% | 能量: ${petStatus.energy}%` :
      '状态未知';

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '宠物状态',
        type: 'normal',
        enabled: false,
        sublabel: statusText
      },
      { type: 'separator' },
      {
        label: '显示宠物',
        type: 'normal',
        click: () => this.emit('show-pet')
      },
      {
        label: '隐藏宠物',
        type: 'normal',
        click: () => this.emit('hide-pet')
      },
      { type: 'separator' },
      {
        label: '快速操作',
        type: 'submenu',
        submenu: [
          {
            label: '喂食 🍎',
            type: 'normal',
            enabled: petStatus ? petStatus.hunger < 80 : true,
            click: () => this.emit('feed-pet')
          },
          {
            label: '玩耍 🎾',
            type: 'normal',
            enabled: petStatus ? petStatus.energy > 20 : true,
            click: () => this.emit('play-with-pet')
          },
          {
            label: '休息 💤',
            type: 'normal',
            enabled: petStatus ? petStatus.energy < 50 : true,
            click: () => this.emit('rest-pet')
          }
        ]
      },
      { type: 'separator' },
      {
        label: '设置',
        type: 'normal',
        click: () => this.emit('open-settings')
      },
      {
        label: '退出',
        type: 'normal',
        click: () => this.emit('quit-app')
      }
    ]);

    this.tray.setContextMenu(contextMenu);
  }

  // 简单的事件发射器实现
  private eventListeners: { [event: string]: Function[] } = {};

  on(event: string, listener: Function): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(listener);
  }

  off(event: string, listener: Function): void {
    if (!this.eventListeners[event]) return;
    const index = this.eventListeners[event].indexOf(listener);
    if (index > -1) {
      this.eventListeners[event].splice(index, 1);
    }
  }

  private emit(event: string, ...args: any[]): void {
    if (!this.eventListeners[event]) return;
    this.eventListeners[event].forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        logger.error(`Error in tray event listener for ${event}:`, error);
      }
    });
  }

  cleanup(): void {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
    this.eventListeners = {};
    logger.info('Tray Manager cleaned up');
  }
}
