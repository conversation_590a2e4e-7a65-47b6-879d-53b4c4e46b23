import { create } from 'zustand';
import { logger } from '@shared/utils/logger';

interface AppState {
  // UI状态
  isSettingsOpen: boolean;
  isAboutOpen: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 应用信息
  version: string;
  platform: string;
  
  // 窗口状态
  windowPosition: { x: number; y: number } | null;
  windowSize: { width: number; height: number } | null;
  isWindowVisible: boolean;
  isWindowFocused: boolean;
  
  // 系统状态
  isOnline: boolean;
  systemTheme: 'light' | 'dark';
  batteryLevel: number | null;
  isOnBattery: boolean;
  
  // 性能监控
  memoryUsage: number;
  cpuUsage: number;
  frameRate: number;
}

interface AppStore extends AppState {
  // UI控制
  setSettingsOpen: (open: boolean) => void;
  setAboutOpen: (open: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 应用信息
  loadAppInfo: () => Promise<void>;
  
  // 窗口控制
  updateWindowPosition: (position: { x: number; y: number }) => void;
  updateWindowSize: (size: { width: number; height: number }) => void;
  setWindowVisible: (visible: boolean) => void;
  setWindowFocused: (focused: boolean) => void;
  
  // 系统状态
  updateSystemInfo: () => Promise<void>;
  setOnlineStatus: (online: boolean) => void;
  updateSystemTheme: (theme: 'light' | 'dark') => void;
  updateBatteryInfo: (level: number, onBattery: boolean) => void;
  
  // 性能监控
  updatePerformanceMetrics: () => void;
  
  // 工具方法
  showNotification: (title: string, message: string) => void;
  openExternalLink: (url: string) => void;
  restartApp: () => void;
  quitApp: () => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始状态
  isSettingsOpen: false,
  isAboutOpen: false,
  isLoading: false,
  error: null,
  
  version: '1.0.0',
  platform: 'unknown',
  
  windowPosition: null,
  windowSize: null,
  isWindowVisible: true,
  isWindowFocused: true,
  
  isOnline: navigator.onLine,
  systemTheme: 'light',
  batteryLevel: null,
  isOnBattery: false,
  
  memoryUsage: 0,
  cpuUsage: 0,
  frameRate: 60,

  // UI控制
  setSettingsOpen: (open: boolean) => {
    set({ isSettingsOpen: open });
    logger.debug('Settings modal:', open ? 'opened' : 'closed');
  },

  setAboutOpen: (open: boolean) => {
    set({ isAboutOpen: open });
    logger.debug('About modal:', open ? 'opened' : 'closed');
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
    if (error) {
      logger.error('App error:', error);
    }
  },

  // 应用信息
  loadAppInfo: async () => {
    try {
      if (window.electronAPI) {
        const [version, platform] = await Promise.all([
          window.electronAPI.getAppVersion(),
          window.electronAPI.getPlatform()
        ]);
        
        set({ version, platform });
        logger.info('App info loaded:', { version, platform });
      }
    } catch (error) {
      logger.error('Failed to load app info:', error);
    }
  },

  // 窗口控制
  updateWindowPosition: (position: { x: number; y: number }) => {
    set({ windowPosition: position });
  },

  updateWindowSize: (size: { width: number; height: number }) => {
    set({ windowSize: size });
  },

  setWindowVisible: (visible: boolean) => {
    set({ isWindowVisible: visible });
  },

  setWindowFocused: (focused: boolean) => {
    set({ isWindowFocused: focused });
  },

  // 系统状态
  updateSystemInfo: async () => {
    try {
      if (window.electronAPI) {
        const screenSize = await window.electronAPI.getScreenSize();
        logger.debug('Screen size:', screenSize);
      }
    } catch (error) {
      logger.error('Failed to update system info:', error);
    }
  },

  setOnlineStatus: (online: boolean) => {
    set({ isOnline: online });
    logger.debug('Online status:', online);
  },

  updateSystemTheme: (theme: 'light' | 'dark') => {
    set({ systemTheme: theme });
    logger.debug('System theme changed:', theme);
  },

  updateBatteryInfo: (level: number, onBattery: boolean) => {
    set({ batteryLevel: level, isOnBattery: onBattery });
  },

  // 性能监控
  updatePerformanceMetrics: () => {
    if (typeof performance !== 'undefined' && performance.memory) {
      const memory = (performance.memory as any);
      const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
      set({ memoryUsage });
    }
  },

  // 工具方法
  showNotification: (title: string, message: string) => {
    if (window.electronAPI) {
      window.electronAPI.showNotification(title, message);
    } else if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, { body: message });
    }
  },

  openExternalLink: (url: string) => {
    if (window.electronAPI) {
      window.electronAPI.openExternal(url);
    } else {
      window.open(url, '_blank');
    }
  },

  restartApp: () => {
    if (window.electronAPI) {
      window.electronAPI.restartApp();
    }
  },

  quitApp: () => {
    if (window.electronAPI) {
      window.electronAPI.quitApp();
    }
  }
}));

// 监听在线状态变化
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    useAppStore.getState().setOnlineStatus(true);
  });

  window.addEventListener('offline', () => {
    useAppStore.getState().setOnlineStatus(false);
  });

  // 监听窗口焦点变化
  window.addEventListener('focus', () => {
    useAppStore.getState().setWindowFocused(true);
  });

  window.addEventListener('blur', () => {
    useAppStore.getState().setWindowFocused(false);
  });

  // 定期更新性能指标
  setInterval(() => {
    useAppStore.getState().updatePerformanceMetrics();
  }, 5000);
}
