import React from 'react';
import { motion } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { clsx } from 'clsx';

interface StatusBarProps {
  className?: string;
}

export const StatusBar: React.FC<StatusBarProps> = ({ className }) => {
  const { currentPet } = usePetStore();

  if (!currentPet) return null;

  const { stats } = currentPet;

  // 获取状态颜色
  const getStatColor = (value: number) => {
    if (value > 80) return 'bg-green-400';
    if (value > 60) return 'bg-yellow-400';
    if (value > 40) return 'bg-orange-400';
    return 'bg-red-400';
  };

  // 获取状态图标
  const getStatIcon = (statType: string) => {
    switch (statType) {
      case 'hunger': return '🍎';
      case 'happiness': return '😊';
      case 'health': return '❤️';
      case 'energy': return '⚡';
      case 'cleanliness': return '🛁';
      default: return '📊';
    }
  };

  const statItems = [
    { key: 'hunger', label: '饥饿', value: stats.hunger },
    { key: 'happiness', label: '快乐', value: stats.happiness },
    { key: 'health', label: '健康', value: stats.health },
    { key: 'energy', label: '能量', value: stats.energy },
    { key: 'cleanliness', label: '清洁', value: stats.cleanliness }
  ];

  return (
    <motion.div
      className={clsx(
        "bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg",
        "border border-gray-200",
        className
      )}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      {/* 宠物信息头部 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-lg">
            {currentPet.type === 'cat' && '🐱'}
            {currentPet.type === 'dog' && '🐶'}
            {currentPet.type === 'rabbit' && '🐰'}
            {currentPet.type === 'bird' && '🐦'}
          </span>
          <span className="font-medium text-gray-800">{currentPet.name}</span>
          <span className="text-sm text-gray-500">Lv.{currentPet.level}</span>
        </div>
        
        {/* 心情指示器 */}
        <div className="text-lg">
          {currentPet.mood === 'happy' && '😊'}
          {currentPet.mood === 'sad' && '😢'}
          {currentPet.mood === 'angry' && '😠'}
          {currentPet.mood === 'sleepy' && '😴'}
          {currentPet.mood === 'excited' && '🤩'}
          {currentPet.mood === 'neutral' && '😐'}
        </div>
      </div>

      {/* 状态条 */}
      <div className="space-y-2">
        {statItems.map(({ key, label, value }) => (
          <div key={key} className="flex items-center space-x-2">
            {/* 图标 */}
            <span className="text-sm">{getStatIcon(key)}</span>
            
            {/* 标签 */}
            <span className="text-xs text-gray-600 w-8">{label}</span>
            
            {/* 进度条 */}
            <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div
                className={clsx("h-full rounded-full", getStatColor(value))}
                initial={{ width: 0 }}
                animate={{ width: `${value}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
            
            {/* 数值 */}
            <span className="text-xs text-gray-500 w-8 text-right">
              {Math.round(value)}
            </span>
          </div>
        ))}
      </div>

      {/* 经验条 */}
      <div className="mt-3 pt-2 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          <span className="text-sm">✨</span>
          <span className="text-xs text-gray-600">经验</span>
          <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${(currentPet.experience % 100)}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          <span className="text-xs text-gray-500">
            {currentPet.experience}/100
          </span>
        </div>
      </div>

      {/* 警告指示器 */}
      {(stats.hunger < 30 || stats.health < 30 || stats.energy < 20) && (
        <motion.div
          className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600 text-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          ⚠️ 宠物需要照顾！
        </motion.div>
      )}
    </motion.div>
  );
};
