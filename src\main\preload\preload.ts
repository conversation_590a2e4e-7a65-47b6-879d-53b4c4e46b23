import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IPCEvents } from '@shared/constants/ipc-events';

// 定义暴露给渲染进程的API
const electronAPI = {
  // 窗口控制
  setWindowPosition: (x: number, y: number) => 
    ipcRenderer.invoke(IPCEvents.WINDOW_SET_POSITION, x, y),
  getWindowPosition: () => 
    ipcRenderer.invoke(IPCEvents.WINDOW_GET_POSITION),
  setWindowSize: (width: number, height: number) => 
    ipcRenderer.invoke(IPCEvents.WINDOW_SET_SIZE, width, height),
  setAlwaysOnTop: (alwaysOnTop: boolean) => 
    ipcRenderer.invoke(IPCEvents.WINDOW_SET_ALWAYS_ON_TOP, alwaysOnTop),
  showWindow: () => 
    ipcRenderer.invoke(IPCEvents.WINDOW_SHOW),
  hideWindow: () => 
    ipcRenderer.invoke(IPCEvents.WINDOW_HIDE),
  toggleWindow: () => 
    ipcRenderer.invoke(IPCEvents.WINDOW_TOGGLE),

  // 应用控制
  quitApp: () => 
    ipcRenderer.invoke(IPCEvents.APP_QUIT),
  restartApp: () => 
    ipcRenderer.invoke(IPCEvents.APP_RESTART),
  getAppVersion: () => 
    ipcRenderer.invoke(IPCEvents.APP_GET_VERSION),
  getPlatform: () => 
    ipcRenderer.invoke(IPCEvents.APP_GET_PLATFORM),
  setAutoStart: (autoStart: boolean) => 
    ipcRenderer.invoke(IPCEvents.APP_SET_AUTO_START, autoStart),

  // 系统信息
  getScreenSize: () => 
    ipcRenderer.invoke(IPCEvents.SYSTEM_GET_SCREEN_SIZE),
  getCursorPosition: () => 
    ipcRenderer.invoke(IPCEvents.SYSTEM_GET_CURSOR_POSITION),

  // 设置
  openSettings: () => 
    ipcRenderer.invoke(IPCEvents.SETTINGS_OPEN),

  // 托盘
  updateTrayIcon: (iconType: string) => 
    ipcRenderer.invoke(IPCEvents.TRAY_UPDATE_ICON, iconType),
  showBalloon: (title: string, content: string, iconType?: string) => 
    ipcRenderer.invoke(IPCEvents.TRAY_SHOW_BALLOON, title, content, iconType),
  updateTooltip: (text: string) => 
    ipcRenderer.invoke(IPCEvents.TRAY_UPDATE_TOOLTIP, text),

  // 文件系统
  readFile: (filePath: string) => 
    ipcRenderer.invoke(IPCEvents.FS_READ_FILE, filePath),
  writeFile: (filePath: string, content: string) => 
    ipcRenderer.invoke(IPCEvents.FS_WRITE_FILE, filePath, content),
  deleteFile: (filePath: string) => 
    ipcRenderer.invoke(IPCEvents.FS_DELETE_FILE, filePath),

  // 通知
  showNotification: (title: string, body: string, options?: any) => 
    ipcRenderer.invoke(IPCEvents.NOTIFICATION_SHOW, title, body, options),

  // 错误报告
  reportError: (error: any) => 
    ipcRenderer.invoke(IPCEvents.ERROR_REPORT, error),

  // 外部链接
  openExternal: (url: string) => 
    ipcRenderer.invoke('shell:open-external', url),

  // 事件监听
  on: (channel: string, callback: Function) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },
  
  off: (channel: string, callback: Function) => {
    ipcRenderer.removeListener(channel, callback as any);
  },

  // 一次性事件监听
  once: (channel: string, callback: Function) => {
    ipcRenderer.once(channel, (event, ...args) => callback(...args));
  }
};

// 暴露API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}

// 安全检查
window.addEventListener('DOMContentLoaded', () => {
  // 移除可能的安全风险
  delete (window as any).require;
  delete (window as any).exports;
  delete (window as any).module;
});
