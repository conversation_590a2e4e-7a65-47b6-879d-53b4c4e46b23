import { BrowserWindow, screen, app } from 'electron';
import { join } from 'path';
import { logger } from '@shared/utils/logger';
import { WindowConfig } from '@shared/types/window';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private settingsWindow: BrowserWindow | null = null;

  async initialize(): Promise<void> {
    logger.info('Initializing Window Manager...');
  }

  async createMainWindow(): Promise<BrowserWindow> {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.focus();
      return this.mainWindow;
    }

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // 宠物窗口配置
    const windowConfig: WindowConfig = {
      width: 200,
      height: 200,
      x: Math.floor(width * 0.8), // 默认位置在屏幕右侧
      y: Math.floor(height * 0.7),
      frame: false, // 无边框窗口
      transparent: true, // 透明背景
      alwaysOnTop: true, // 始终置顶
      skipTaskbar: true, // 不显示在任务栏
      resizable: false, // 不可调整大小
      minimizable: false, // 不可最小化
      maximizable: false, // 不可最大化
      closable: false, // 不显示关闭按钮
      focusable: true, // 可获得焦点
      show: false, // 创建时不显示，等待ready事件
      webPreferences: {
        nodeIntegration: false, // 安全设置
        contextIsolation: true, // 启用上下文隔离
        enableRemoteModule: false, // 禁用remote模块
        preload: join(__dirname, '../preload/preload.js'), // 预加载脚本
        sandbox: false, // 暂时禁用沙箱以便开发
        webSecurity: true, // 启用web安全
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      }
    };

    this.mainWindow = new BrowserWindow(windowConfig);

    // 设置窗口属性
    this.setupMainWindow();

    // 加载应用内容
    await this.loadMainWindow();

    return this.mainWindow;
  }

  private setupMainWindow(): void {
    if (!this.mainWindow) return;

    // 窗口事件监听
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show();
        logger.info('Main window is ready and shown');
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      logger.info('Main window closed');
    });

    this.mainWindow.on('moved', () => {
      if (this.mainWindow) {
        const [x, y] = this.mainWindow.getPosition();
        logger.debug('Window moved to:', { x, y });
        // 这里可以保存窗口位置到配置
      }
    });

    // 防止窗口移出屏幕
    this.mainWindow.on('move', () => {
      if (!this.mainWindow) return;
      
      const [x, y] = this.mainWindow.getPosition();
      const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
      const [windowWidth, windowHeight] = this.mainWindow.getSize();

      let newX = x;
      let newY = y;

      // 确保窗口不会完全移出屏幕
      if (x + windowWidth < 50) newX = 50 - windowWidth;
      if (x > screenWidth - 50) newX = screenWidth - 50;
      if (y + windowHeight < 50) newY = 50 - windowHeight;
      if (y > screenHeight - 50) newY = screenHeight - 50;

      if (newX !== x || newY !== y) {
        this.mainWindow.setPosition(newX, newY);
      }
    });

    // 右键菜单处理
    this.mainWindow.webContents.on('context-menu', (event, params) => {
      // 这里可以显示自定义右键菜单
      logger.debug('Context menu requested at:', { x: params.x, y: params.y });
    });

    // 开发者工具 (仅在开发模式)
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.webContents.openDevTools({ mode: 'detach' });
    }
  }

  private async loadMainWindow(): Promise<void> {
    if (!this.mainWindow) return;

    try {
      if (process.env.NODE_ENV === 'development') {
        // 开发模式：加载开发服务器
        await this.mainWindow.loadURL('http://localhost:3000');
      } else {
        // 生产模式：加载打包后的文件
        await this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
      }
      logger.info('Main window content loaded');
    } catch (error) {
      logger.error('Failed to load main window content:', error);
      throw error;
    }
  }

  async createSettingsWindow(): Promise<BrowserWindow> {
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.focus();
      return this.settingsWindow;
    }

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;

    const settingsConfig: WindowConfig = {
      width: 800,
      height: 600,
      x: Math.floor((width - 800) / 2),
      y: Math.floor((height - 600) / 2),
      frame: true,
      transparent: false,
      alwaysOnTop: false,
      skipTaskbar: false,
      resizable: true,
      minimizable: true,
      maximizable: true,
      closable: true,
      show: false,
      parent: this.mainWindow || undefined,
      modal: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: join(__dirname, '../preload/preload.js'),
        sandbox: false,
        webSecurity: true
      }
    };

    this.settingsWindow = new BrowserWindow(settingsConfig);

    this.settingsWindow.once('ready-to-show', () => {
      if (this.settingsWindow) {
        this.settingsWindow.show();
      }
    });

    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null;
    });

    // 加载设置页面
    if (process.env.NODE_ENV === 'development') {
      await this.settingsWindow.loadURL('http://localhost:3000#/settings');
    } else {
      await this.settingsWindow.loadFile(join(__dirname, '../renderer/index.html'), {
        hash: 'settings'
      });
    }

    return this.settingsWindow;
  }

  showMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  hideMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.hide();
    }
  }

  toggleMainWindow(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isVisible()) {
        this.hideMainWindow();
      } else {
        this.showMainWindow();
      }
    }
  }

  setAlwaysOnTop(alwaysOnTop: boolean): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.setAlwaysOnTop(alwaysOnTop);
    }
  }

  setPosition(x: number, y: number): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.setPosition(x, y);
    }
  }

  getPosition(): { x: number; y: number } | null {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      const [x, y] = this.mainWindow.getPosition();
      return { x, y };
    }
    return null;
  }

  cleanup(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.close();
    }
    logger.info('Window Manager cleaned up');
  }
}
