# 开发指南

## 开发环境搭建

### 系统要求
- Node.js 18.0+ 
- npm 8.0+ 或 yarn 1.22+
- Git 2.30+
- VS Code (推荐)

### 环境安装

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/desktop-pet.git
cd desktop-pet
```

#### 2. 安装依赖
```bash
npm install
# 或
yarn install
```

#### 3. 开发环境配置
```bash
# 复制环境配置文件
cp .env.example .env.local

# 安装开发工具
npm install -g electron
```

### 开发工具配置

#### VS Code 扩展推荐
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer

#### VS Code 设置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## 项目结构详解

```
desktop-pet/
├── src/                    # 源代码目录
│   ├── main/              # Electron 主进程
│   │   ├── main.ts        # 主进程入口
│   │   ├── window/        # 窗口管理
│   │   ├── tray/          # 托盘管理
│   │   └── ipc/           # 进程间通信
│   ├── renderer/          # 渲染进程
│   │   ├── components/    # React 组件
│   │   │   ├── Pet/       # 宠物相关组件
│   │   │   ├── Settings/  # 设置相关组件
│   │   │   └── Common/    # 通用组件
│   │   ├── hooks/         # 自定义 Hooks
│   │   ├── stores/        # 状态管理
│   │   ├── utils/         # 工具函数
│   │   ├── types/         # TypeScript 类型
│   │   └── styles/        # 样式文件
│   ├── shared/            # 共享代码
│   │   ├── constants/     # 常量定义
│   │   ├── types/         # 共享类型
│   │   └── utils/         # 共享工具
│   └── assets/            # 静态资源
│       ├── images/        # 图片资源
│       ├── animations/    # 动画文件
│       └── sounds/        # 音频文件
├── docs/                  # 项目文档
├── tests/                 # 测试文件
├── scripts/               # 构建脚本
├── dist/                  # 构建输出
└── release/               # 发布文件
```

## 开发流程

### 1. 启动开发服务器
```bash
# 启动渲染进程开发服务器
npm run dev:renderer

# 启动主进程（新终端）
npm run dev:main

# 或者一键启动
npm run dev
```

### 2. 代码规范

#### TypeScript 规范
- 使用严格的 TypeScript 配置
- 为所有函数和变量添加类型注解
- 使用接口定义数据结构
- 避免使用 `any` 类型

```typescript
// 好的示例
interface PetState {
  hunger: number;
  happiness: number;
  health: number;
  energy: number;
}

const updatePetState = (state: PetState, delta: Partial<PetState>): PetState => {
  return { ...state, ...delta };
};

// 避免的示例
const updatePetState = (state: any, delta: any): any => {
  return { ...state, ...delta };
};
```

#### React 组件规范
- 使用函数组件和 Hooks
- 组件名使用 PascalCase
- Props 接口以组件名 + Props 命名
- 使用 React.memo 优化性能

```typescript
interface PetWidgetProps {
  petType: string;
  position: { x: number; y: number };
  onMove: (position: { x: number; y: number }) => void;
}

const PetWidget: React.FC<PetWidgetProps> = React.memo(({ 
  petType, 
  position, 
  onMove 
}) => {
  // 组件实现
});
```

#### 样式规范
- 使用 Tailwind CSS 类名
- 自定义样式使用 CSS Modules
- 遵循 BEM 命名规范（自定义样式）

```typescript
// 使用 Tailwind
<div className="flex items-center justify-center p-4 bg-blue-500 rounded-lg">

// 自定义样式
import styles from './PetWidget.module.css';
<div className={styles.petContainer}>
```

### 3. 状态管理

使用 Zustand 进行状态管理：

```typescript
// stores/petStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface PetStore {
  // 状态
  hunger: number;
  happiness: number;
  health: number;
  energy: number;
  
  // 动作
  updateHunger: (value: number) => void;
  updateHappiness: (value: number) => void;
  feed: () => void;
  play: () => void;
}

export const usePetStore = create<PetStore>()(
  persist(
    (set, get) => ({
      hunger: 100,
      happiness: 100,
      health: 100,
      energy: 100,
      
      updateHunger: (value) => set({ hunger: Math.max(0, Math.min(100, value)) }),
      updateHappiness: (value) => set({ happiness: Math.max(0, Math.min(100, value)) }),
      
      feed: () => {
        const { hunger, happiness } = get();
        set({
          hunger: Math.min(100, hunger + 20),
          happiness: Math.min(100, happiness + 10)
        });
      },
      
      play: () => {
        const { happiness, energy } = get();
        set({
          happiness: Math.min(100, happiness + 15),
          energy: Math.max(0, energy - 10)
        });
      }
    }),
    {
      name: 'pet-storage'
    }
  )
);
```

### 4. 组件开发

#### 创建新组件
```bash
# 使用脚本创建组件模板
npm run create:component ComponentName
```

#### 组件模板
```typescript
// components/Pet/PetWidget.tsx
import React, { useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';

interface PetWidgetProps {
  className?: string;
}

export const PetWidget: React.FC<PetWidgetProps> = ({ className }) => {
  const { hunger, happiness, feed, play } = usePetStore();
  
  const handleClick = useCallback(() => {
    if (hunger < 50) {
      feed();
    } else {
      play();
    }
  }, [hunger, feed, play]);
  
  useEffect(() => {
    // 组件挂载时的逻辑
    return () => {
      // 清理逻辑
    };
  }, []);
  
  return (
    <motion.div
      className={`pet-widget ${className}`}
      onClick={handleClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {/* 组件内容 */}
    </motion.div>
  );
};
```

### 5. 测试

#### 单元测试
```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test PetWidget.test.tsx

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

#### 测试示例
```typescript
// tests/components/PetWidget.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { PetWidget } from '../../src/renderer/components/Pet/PetWidget';

describe('PetWidget', () => {
  it('should render correctly', () => {
    render(<PetWidget />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
  
  it('should handle click events', () => {
    const mockOnClick = jest.fn();
    render(<PetWidget onClick={mockOnClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });
});
```

### 6. 构建和打包

#### 开发构建
```bash
npm run build:dev
```

#### 生产构建
```bash
npm run build
```

#### 打包应用
```bash
# 打包当前平台
npm run pack

# 打包所有平台
npm run pack:all

# 打包 Windows
npm run pack:win

# 打包 macOS
npm run pack:mac

# 打包 Linux
npm run pack:linux
```

## 调试指南

### 1. 主进程调试
```bash
# 启动调试模式
npm run debug:main
```

### 2. 渲染进程调试
- 使用 Chrome DevTools
- 在应用中按 F12 打开开发者工具

### 3. 日志记录
```typescript
// 使用统一的日志系统
import { logger } from '../utils/logger';

logger.info('Pet state updated', { hunger: 80, happiness: 90 });
logger.error('Failed to save pet data', error);
logger.debug('Animation frame rendered', { frame: 42 });
```

## 性能优化

### 1. React 性能优化
- 使用 React.memo 包装组件
- 使用 useCallback 和 useMemo
- 避免在渲染中创建新对象

### 2. Electron 性能优化
- 合理使用进程间通信
- 优化资源加载
- 内存管理

### 3. 动画性能优化
- 使用 CSS transforms
- 避免频繁的 DOM 操作
- 实现动画对象池

## 常见问题

### 1. 开发环境问题
**Q: npm install 失败**
A: 尝试清除缓存 `npm cache clean --force` 或使用 yarn

**Q: Electron 启动失败**
A: 检查 Node.js 版本，确保使用 18.0+

### 2. 构建问题
**Q: 打包失败**
A: 检查 electron-builder 配置，确保所有依赖正确安装

### 3. 性能问题
**Q: 应用卡顿**
A: 使用 React DevTools Profiler 分析性能瓶颈
