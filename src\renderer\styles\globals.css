@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义CSS变量 */
:root {
  /* 主色调 */
  --primary-color: #FF6B9D;
  --primary-light: #FFB3D1;
  --primary-dark: #E55A8A;
  
  /* 次要颜色 */
  --secondary-color: #4ECDC4;
  --secondary-light: #7EDDD6;
  --secondary-dark: #3BA99F;
  
  /* 中性色 */
  --neutral-100: #FFFFFF;
  --neutral-200: #F8F9FA;
  --neutral-300: #E9ECEF;
  --neutral-400: #CED4DA;
  --neutral-500: #6C757D;
  --neutral-600: #495057;
  --neutral-700: #343A40;
  --neutral-800: #212529;
  --neutral-900: #000000;
  
  /* 状态颜色 */
  --success-color: #28A745;
  --warning-color: #FFC107;
  --error-color: #DC3545;
  --info-color: #17A2B8;
  
  /* 字体 */
  --font-primary: 'Inter', 'Noto Sans SC', sans-serif;
  --font-heading: 'Poppins', 'Noto Sans SC', sans-serif;
  --font-mono: 'JetBrains Mono', 'Consolas', monospace;
  
  /* 间距 */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* 缓动函数 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* 暗色主题 */
.dark {
  --neutral-100: #1A1A1A;
  --neutral-200: #2D2D2D;
  --neutral-300: #404040;
  --neutral-400: #525252;
  --neutral-500: #737373;
  --neutral-600: #A3A3A3;
  --neutral-700: #D4D4D4;
  --neutral-800: #E5E5E5;
  --neutral-900: #FFFFFF;
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  color: var(--neutral-800);
  background-color: transparent;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -webkit-app-region: no-drag;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-200);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-light);
  color: var(--neutral-900);
}

::-moz-selection {
  background-color: var(--primary-light);
  color: var(--neutral-900);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-in-out);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 输入框基础样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: var(--neutral-100);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  transition: all var(--duration-normal) var(--ease-in-out);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-in-out);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* 工具类 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(255, 107, 157, 0.3);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce 2s ease-in-out infinite;
}

/* 动画定义 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0px);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --neutral-300: #000000;
    --neutral-700: #FFFFFF;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
}
