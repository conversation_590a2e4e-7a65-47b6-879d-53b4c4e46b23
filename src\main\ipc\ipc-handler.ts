import { ipc<PERSON>ain, BrowserWindow, app, screen } from 'electron';
import { logger } from '@shared/utils/logger';
import { IPCEvents } from '@shared/constants/ipc-events';
import { WindowManager } from '../window/window-manager';
import { TrayManager } from '../tray/tray-manager';

export class IPCHandler {
  private windowManager: WindowManager | null = null;
  private trayManager: TrayManager | null = null;

  initialize(): void {
    this.setupIPCHandlers();
    logger.info('IPC Handler initialized');
  }

  setManagers(windowManager: WindowManager, trayManager: TrayManager): void {
    this.windowManager = windowManager;
    this.trayManager = trayManager;
  }

  private setupIPCHandlers(): void {
    // 窗口控制相关
    ipcMain.handle(IPCEvents.WINDOW_SET_POSITION, this.handleSetWindowPosition);
    ipcMain.handle(IPCEvents.WINDOW_GET_POSITION, this.handleGetWindowPosition);
    ipcMain.handle(IPCEvents.WINDOW_SET_SIZE, this.handleSetWindowSize);
    ipcMain.handle(IPCEvents.WINDOW_SET_ALWAYS_ON_TOP, this.handleSetAlwaysOnTop);
    ipcMain.handle(IPCEvents.WINDOW_SHOW, this.handleShowWindow);
    ipcMain.handle(IPCEvents.WINDOW_HIDE, this.handleHideWindow);
    ipcMain.handle(IPCEvents.WINDOW_TOGGLE, this.handleToggleWindow);

    // 应用控制相关
    ipcMain.handle(IPCEvents.APP_QUIT, this.handleQuitApp);
    ipcMain.handle(IPCEvents.APP_RESTART, this.handleRestartApp);
    ipcMain.handle(IPCEvents.APP_GET_VERSION, this.handleGetAppVersion);
    ipcMain.handle(IPCEvents.APP_GET_PLATFORM, this.handleGetPlatform);

    // 系统信息相关
    ipcMain.handle(IPCEvents.SYSTEM_GET_SCREEN_SIZE, this.handleGetScreenSize);
    ipcMain.handle(IPCEvents.SYSTEM_GET_CURSOR_POSITION, this.handleGetCursorPosition);

    // 设置相关
    ipcMain.handle(IPCEvents.SETTINGS_OPEN, this.handleOpenSettings);

    // 托盘相关
    ipcMain.handle(IPCEvents.TRAY_UPDATE_ICON, this.handleUpdateTrayIcon);
    ipcMain.handle(IPCEvents.TRAY_SHOW_BALLOON, this.handleShowBalloon);
    ipcMain.handle(IPCEvents.TRAY_UPDATE_TOOLTIP, this.handleUpdateTooltip);

    // 文件系统相关
    ipcMain.handle(IPCEvents.FS_READ_FILE, this.handleReadFile);
    ipcMain.handle(IPCEvents.FS_WRITE_FILE, this.handleWriteFile);
    ipcMain.handle(IPCEvents.FS_DELETE_FILE, this.handleDeleteFile);

    // 通知相关
    ipcMain.handle(IPCEvents.NOTIFICATION_SHOW, this.handleShowNotification);

    logger.info('IPC handlers registered');
  }

  // 窗口控制处理器
  private handleSetWindowPosition = async (
    event: Electron.IpcMainInvokeEvent,
    x: number,
    y: number
  ): Promise<void> => {
    try {
      if (this.windowManager) {
        this.windowManager.setPosition(x, y);
      }
    } catch (error) {
      logger.error('Failed to set window position:', error);
      throw error;
    }
  };

  private handleGetWindowPosition = async (
    event: Electron.IpcMainInvokeEvent
  ): Promise<{ x: number; y: number } | null> => {
    try {
      return this.windowManager?.getPosition() || null;
    } catch (error) {
      logger.error('Failed to get window position:', error);
      throw error;
    }
  };

  private handleSetWindowSize = async (
    event: Electron.IpcMainInvokeEvent,
    width: number,
    height: number
  ): Promise<void> => {
    try {
      const mainWindow = BrowserWindow.fromWebContents(event.sender);
      if (mainWindow) {
        mainWindow.setSize(width, height);
      }
    } catch (error) {
      logger.error('Failed to set window size:', error);
      throw error;
    }
  };

  private handleSetAlwaysOnTop = async (
    event: Electron.IpcMainInvokeEvent,
    alwaysOnTop: boolean
  ): Promise<void> => {
    try {
      if (this.windowManager) {
        this.windowManager.setAlwaysOnTop(alwaysOnTop);
      }
    } catch (error) {
      logger.error('Failed to set always on top:', error);
      throw error;
    }
  };

  private handleShowWindow = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      if (this.windowManager) {
        this.windowManager.showMainWindow();
      }
    } catch (error) {
      logger.error('Failed to show window:', error);
      throw error;
    }
  };

  private handleHideWindow = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      if (this.windowManager) {
        this.windowManager.hideMainWindow();
      }
    } catch (error) {
      logger.error('Failed to hide window:', error);
      throw error;
    }
  };

  private handleToggleWindow = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      if (this.windowManager) {
        this.windowManager.toggleMainWindow();
      }
    } catch (error) {
      logger.error('Failed to toggle window:', error);
      throw error;
    }
  };

  // 应用控制处理器
  private handleQuitApp = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      app.quit();
    } catch (error) {
      logger.error('Failed to quit app:', error);
      throw error;
    }
  };

  private handleRestartApp = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      app.relaunch();
      app.quit();
    } catch (error) {
      logger.error('Failed to restart app:', error);
      throw error;
    }
  };

  private handleGetAppVersion = async (event: Electron.IpcMainInvokeEvent): Promise<string> => {
    return app.getVersion();
  };

  private handleGetPlatform = async (event: Electron.IpcMainInvokeEvent): Promise<string> => {
    return process.platform;
  };

  // 系统信息处理器
  private handleGetScreenSize = async (
    event: Electron.IpcMainInvokeEvent
  ): Promise<{ width: number; height: number }> => {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    return { width, height };
  };

  private handleGetCursorPosition = async (
    event: Electron.IpcMainInvokeEvent
  ): Promise<{ x: number; y: number }> => {
    return screen.getCursorScreenPoint();
  };

  // 设置处理器
  private handleOpenSettings = async (event: Electron.IpcMainInvokeEvent): Promise<void> => {
    try {
      if (this.windowManager) {
        await this.windowManager.createSettingsWindow();
      }
    } catch (error) {
      logger.error('Failed to open settings:', error);
      throw error;
    }
  };

  // 托盘处理器
  private handleUpdateTrayIcon = async (
    event: Electron.IpcMainInvokeEvent,
    iconType: string
  ): Promise<void> => {
    try {
      if (this.trayManager) {
        this.trayManager.updateTrayIcon(iconType as any);
      }
    } catch (error) {
      logger.error('Failed to update tray icon:', error);
      throw error;
    }
  };

  private handleShowBalloon = async (
    event: Electron.IpcMainInvokeEvent,
    title: string,
    content: string,
    iconType: string = 'info'
  ): Promise<void> => {
    try {
      if (this.trayManager) {
        this.trayManager.showBalloon(title, content, iconType as any);
      }
    } catch (error) {
      logger.error('Failed to show balloon:', error);
      throw error;
    }
  };

  private handleUpdateTooltip = async (
    event: Electron.IpcMainInvokeEvent,
    text: string
  ): Promise<void> => {
    try {
      if (this.trayManager) {
        this.trayManager.updateTooltip(text);
      }
    } catch (error) {
      logger.error('Failed to update tooltip:', error);
      throw error;
    }
  };

  // 文件系统处理器
  private handleReadFile = async (
    event: Electron.IpcMainInvokeEvent,
    filePath: string
  ): Promise<string> => {
    try {
      const fs = await import('fs/promises');
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      logger.error('Failed to read file:', error);
      throw error;
    }
  };

  private handleWriteFile = async (
    event: Electron.IpcMainInvokeEvent,
    filePath: string,
    content: string
  ): Promise<void> => {
    try {
      const fs = await import('fs/promises');
      await fs.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      logger.error('Failed to write file:', error);
      throw error;
    }
  };

  private handleDeleteFile = async (
    event: Electron.IpcMainInvokeEvent,
    filePath: string
  ): Promise<void> => {
    try {
      const fs = await import('fs/promises');
      await fs.unlink(filePath);
    } catch (error) {
      logger.error('Failed to delete file:', error);
      throw error;
    }
  };

  // 通知处理器
  private handleShowNotification = async (
    event: Electron.IpcMainInvokeEvent,
    title: string,
    body: string,
    options?: any
  ): Promise<void> => {
    try {
      const { Notification } = await import('electron');
      if (Notification.isSupported()) {
        new Notification({
          title,
          body,
          ...options
        }).show();
      }
    } catch (error) {
      logger.error('Failed to show notification:', error);
      throw error;
    }
  };

  cleanup(): void {
    // 移除所有IPC监听器
    ipcMain.removeAllListeners();
    logger.info('IPC Handler cleaned up');
  }
}
