import React, { useCallback, useEffect, useRef, useState } from 'react';
import { motion, useAnimation, PanInfo } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { logger } from '@shared/utils/logger';
import { clsx } from 'clsx';

interface PetWidgetProps {
  className?: string;
}

export const PetWidget: React.FC<PetWidgetProps> = ({ className }) => {
  const controls = useAnimation();
  const petRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [lastClickTime, setLastClickTime] = useState(0);

  // 状态管理
  const { 
    currentPet, 
    updatePetPosition, 
    feedPet, 
    playWithPet, 
    petPet,
    updatePetAnimation 
  } = usePetStore();
  
  const { settings } = useSettingsStore();

  // 处理拖拽
  const handleDragStart = useCallback(() => {
    setIsDragging(true);
    updatePetAnimation('walk');
    logger.debug('Pet drag started');
  }, [updatePetAnimation]);

  const handleDragEnd = useCallback((event: any, info: PanInfo) => {
    setIsDragging(false);
    
    if (currentPet) {
      const newPosition = {
        x: currentPet.position.x + info.offset.x,
        y: currentPet.position.y + info.offset.y
      };
      
      updatePetPosition(newPosition);
      
      // 通知主进程更新窗口位置
      window.electronAPI?.setWindowPosition(newPosition.x, newPosition.y);
    }
    
    updatePetAnimation('idle');
    logger.debug('Pet drag ended', { offset: info.offset });
  }, [currentPet, updatePetPosition, updatePetAnimation]);

  // 处理点击
  const handleClick = useCallback(() => {
    const now = Date.now();
    const timeSinceLastClick = now - lastClickTime;
    
    if (timeSinceLastClick < 300) {
      // 双击
      handleDoubleClick();
    } else {
      // 单击
      petPet();
    }
    
    setLastClickTime(now);
  }, [lastClickTime, petPet]);

  // 处理双击
  const handleDoubleClick = useCallback(() => {
    if (currentPet) {
      if (currentPet.stats.hunger < 50) {
        feedPet();
      } else if (currentPet.stats.energy > 30) {
        playWithPet();
      } else {
        petPet();
      }
    }
  }, [currentPet, feedPet, playWithPet, petPet]);

  // 处理右键点击
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    // 右键菜单由App组件处理
  }, []);

  // 自动动画循环
  useEffect(() => {
    if (!currentPet || isDragging) return;

    const animationInterval = setInterval(() => {
      // 根据宠物状态播放不同动画
      if (currentPet.stats.energy < 20) {
        updatePetAnimation('sleep');
      } else if (currentPet.stats.happiness > 80) {
        updatePetAnimation('happy');
      } else if (currentPet.stats.hunger < 30) {
        updatePetAnimation('sad');
      } else {
        updatePetAnimation('idle');
      }
    }, 5000 + Math.random() * 5000); // 5-10秒随机间隔

    return () => clearInterval(animationInterval);
  }, [currentPet, isDragging, updatePetAnimation]);

  // 获取宠物精灵图片
  const getPetSprite = useCallback(() => {
    if (!currentPet) return '';
    
    const { type, currentAnimation, mood } = currentPet;
    return `/assets/sprites/${type}/${currentAnimation}_${mood}.png`;
  }, [currentPet]);

  // 获取宠物状态颜色
  const getStatusColor = useCallback(() => {
    if (!currentPet) return 'text-gray-400';
    
    const avgStats = (
      currentPet.stats.hunger + 
      currentPet.stats.happiness + 
      currentPet.stats.health + 
      currentPet.stats.energy
    ) / 4;
    
    if (avgStats > 80) return 'text-green-400';
    if (avgStats > 60) return 'text-yellow-400';
    if (avgStats > 40) return 'text-orange-400';
    return 'text-red-400';
  }, [currentPet]);

  if (!currentPet) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <div className="text-gray-400">没有宠物</div>
      </div>
    );
  }

  return (
    <motion.div
      ref={petRef}
      className={clsx(
        "relative cursor-pointer select-none",
        "flex items-center justify-center",
        "w-full h-full",
        className
      )}
      drag
      dragMomentum={false}
      dragElastic={0.1}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onClick={handleClick}
      onContextMenu={handleContextMenu}
      animate={controls}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      style={{
        transform: `scale(${currentPet.size})`,
      }}
    >
      {/* 宠物主体 */}
      <motion.div
        className="relative"
        animate={{
          y: currentPet.currentAnimation === 'jump' ? [-10, 0] : 0,
          rotate: isDragging ? [-2, 2, -2] : 0,
        }}
        transition={{
          y: { duration: 0.5, ease: "easeInOut" },
          rotate: { duration: 0.3, repeat: isDragging ? Infinity : 0 }
        }}
      >
        {/* 宠物图像 */}
        <div className="relative w-24 h-24">
          {/* 阴影 */}
          <div 
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-4 bg-black opacity-20 rounded-full blur-sm"
            style={{
              transform: `translateX(-50%) scale(${isDragging ? 1.2 : 1})`,
            }}
          />
          
          {/* 宠物精灵 */}
          <motion.div
            className="relative w-full h-full bg-gradient-to-br from-pink-300 to-purple-400 rounded-full flex items-center justify-center text-4xl"
            animate={{
              scale: currentPet.currentAnimation === 'happy' ? [1, 1.1, 1] : 1,
            }}
            transition={{
              scale: { duration: 0.6, repeat: currentPet.currentAnimation === 'happy' ? Infinity : 0 }
            }}
          >
            {/* 临时使用emoji，实际应该是精灵图 */}
            {currentPet.type === 'cat' && '🐱'}
            {currentPet.type === 'dog' && '🐶'}
            {currentPet.type === 'rabbit' && '🐰'}
            {currentPet.type === 'bird' && '🐦'}
          </motion.div>

          {/* 状态指示器 */}
          {settings.showStats && (
            <motion.div
              className="absolute -top-2 -right-2 w-4 h-4 rounded-full border-2 border-white"
              style={{
                backgroundColor: getStatusColor().replace('text-', '').replace('-400', ''),
              }}
              animate={{
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            />
          )}

          {/* 心情表情 */}
          <motion.div
            className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            {currentPet.mood === 'happy' && '😊'}
            {currentPet.mood === 'sad' && '😢'}
            {currentPet.mood === 'angry' && '😠'}
            {currentPet.mood === 'sleepy' && '😴'}
            {currentPet.mood === 'excited' && '🤩'}
            {currentPet.mood === 'neutral' && '😐'}
          </motion.div>

          {/* 动画效果 */}
          {currentPet.currentAnimation === 'eat' && (
            <motion.div
              className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-lg"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
            >
              🍎
            </motion.div>
          )}

          {currentPet.currentAnimation === 'play' && (
            <motion.div
              className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-lg"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
            >
              🎾
            </motion.div>
          )}

          {currentPet.currentAnimation === 'clean' && (
            <motion.div
              className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-lg"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
            >
              🛁
            </motion.div>
          )}
        </div>

        {/* 宠物名称 */}
        {settings.showPetName && (
          <motion.div
            className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-center text-gray-600 bg-white bg-opacity-80 px-2 py-1 rounded"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {currentPet.name}
          </motion.div>
        )}
      </motion.div>

      {/* 交互提示 */}
      {isDragging && (
        <motion.div
          className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 bg-white bg-opacity-90 px-2 py-1 rounded"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          拖拽移动宠物
        </motion.div>
      )}
    </motion.div>
  );
};
