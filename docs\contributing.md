# 贡献指南

## 欢迎贡献！

感谢您对桌面宠物项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- 🎨 设计改进
- 🌍 翻译和本地化

## 开始之前

### 行为准则

参与本项目即表示您同意遵守我们的[行为准则](CODE_OF_CONDUCT.md)。我们致力于为所有人提供友好、安全和包容的环境。

### 开发环境

在开始贡献之前，请确保您已经：

1. 阅读了[开发指南](development-guide.md)
2. 搭建了本地开发环境
3. 熟悉了项目的技术栈和架构

## 贡献方式

### 报告问题

#### 在提交问题之前
- 搜索现有的 [Issues](https://github.com/your-username/desktop-pet/issues) 确保问题未被报告
- 确保您使用的是最新版本
- 收集相关的系统信息和错误日志

#### 如何报告 Bug
1. 使用 [Bug 报告模板](https://github.com/your-username/desktop-pet/issues/new?template=bug_report.md)
2. 提供清晰的标题和描述
3. 包含重现步骤
4. 附上截图或录屏（如适用）
5. 提供系统环境信息

#### Bug 报告模板
```markdown
**Bug 描述**
简洁清晰地描述 Bug

**重现步骤**
1. 打开应用
2. 点击 '...'
3. 滚动到 '...'
4. 看到错误

**期望行为**
描述您期望发生的行为

**实际行为**
描述实际发生的行为

**截图**
如果适用，添加截图帮助解释问题

**环境信息**
- 操作系统: [例如 Windows 11]
- 应用版本: [例如 1.2.0]
- Node.js 版本: [例如 18.16.0]

**附加信息**
添加任何其他相关信息
```

### 功能建议

#### 提交功能请求
1. 使用 [功能请求模板](https://github.com/your-username/desktop-pet/issues/new?template=feature_request.md)
2. 详细描述功能需求
3. 解释为什么需要这个功能
4. 提供可能的实现方案

#### 功能请求模板
```markdown
**功能描述**
简洁清晰地描述您希望的功能

**问题背景**
描述这个功能要解决的问题

**解决方案**
描述您希望的解决方案

**替代方案**
描述您考虑过的其他解决方案

**附加信息**
添加任何其他相关信息、截图或示例
```

## 代码贡献

### 开发流程

#### 1. Fork 项目
```bash
# Fork 项目到您的 GitHub 账户
# 然后克隆到本地
git clone https://github.com/your-username/desktop-pet.git
cd desktop-pet

# 添加上游仓库
git remote add upstream https://github.com/original-owner/desktop-pet.git
```

#### 2. 创建分支
```bash
# 从 main 分支创建新分支
git checkout -b feature/your-feature-name

# 或者修复 Bug
git checkout -b fix/bug-description
```

#### 3. 开发和测试
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 检查代码质量
npm run lint
npm run type-check
```

#### 4. 提交更改
```bash
# 添加更改
git add .

# 提交更改（遵循提交规范）
git commit -m "feat: add new pet animation system"

# 推送到您的 Fork
git push origin feature/your-feature-name
```

#### 5. 创建 Pull Request
1. 在 GitHub 上创建 Pull Request
2. 填写 PR 模板
3. 等待代码审查
4. 根据反馈进行修改

### 代码规范

#### 提交信息规范
我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(pet): add new cat animation
fix(ui): resolve button click issue
docs(api): update API documentation
style(components): format code with prettier
refactor(store): simplify state management
test(utils): add unit tests for helper functions
chore(deps): update dependencies
```

#### 代码风格

**TypeScript/JavaScript:**
```typescript
// 使用 PascalCase 命名组件
const PetWidget: React.FC<PetWidgetProps> = () => {
  // 使用 camelCase 命名变量和函数
  const [isVisible, setIsVisible] = useState(true);
  
  // 使用 const assertions 和明确的类型
  const petTypes = ['cat', 'dog', 'rabbit'] as const;
  
  // 使用 async/await 而不是 Promise.then
  const handleSave = async () => {
    try {
      await savePetData(petData);
    } catch (error) {
      console.error('Failed to save:', error);
    }
  };
  
  return <div className="pet-widget">{/* ... */}</div>;
};
```

**CSS/样式:**
```css
/* 使用 kebab-case 命名 CSS 类 */
.pet-widget {
  display: flex;
  align-items: center;
}

/* 使用 BEM 命名规范 */
.pet-widget__status {
  color: #333;
}

.pet-widget__status--low {
  color: #ff4444;
}
```

#### 文件组织
```
src/
├── components/
│   ├── Pet/
│   │   ├── PetWidget.tsx
│   │   ├── PetWidget.test.tsx
│   │   ├── PetWidget.module.css
│   │   └── index.ts
│   └── index.ts
├── hooks/
│   ├── usePetState.ts
│   ├── usePetState.test.ts
│   └── index.ts
└── utils/
    ├── petHelpers.ts
    ├── petHelpers.test.ts
    └── index.ts
```

### 测试要求

#### 单元测试
- 所有新功能必须包含单元测试
- 测试覆盖率应保持在 80% 以上
- 使用描述性的测试名称

```typescript
// 好的测试示例
describe('PetWidget', () => {
  it('should display pet with correct initial state', () => {
    // 测试实现
  });
  
  it('should update happiness when pet is clicked', () => {
    // 测试实现
  });
  
  it('should show low health warning when health is below 20', () => {
    // 测试实现
  });
});
```

#### 集成测试
- 测试组件间的交互
- 测试用户工作流程
- 使用 E2E 测试工具

### Pull Request 指南

#### PR 模板
```markdown
## 变更描述
简要描述此 PR 的变更内容

## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重大变更
- [ ] 文档更新
- [ ] 性能优化
- [ ] 代码重构

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 自我审查了代码
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 没有引入新的警告
- [ ] 添加了测试用例
- [ ] 所有测试通过

## 相关 Issue
关闭 #(issue 编号)

## 截图（如适用）
添加截图展示变更效果
```

#### 代码审查

**作为 PR 作者:**
- 确保 PR 专注于单一功能或修复
- 提供清晰的描述和上下文
- 响应审查意见并及时修改
- 保持 PR 更新（rebase 到最新的 main）

**作为审查者:**
- 关注代码质量、可读性和性能
- 提供建设性的反馈
- 检查是否有足够的测试覆盖
- 验证功能是否按预期工作

## 文档贡献

### 文档类型
- **用户文档**: 用户手册、FAQ、教程
- **开发文档**: API 文档、架构说明、开发指南
- **项目文档**: README、贡献指南、变更日志

### 文档规范
- 使用清晰、简洁的语言
- 提供代码示例和截图
- 保持文档与代码同步
- 使用 Markdown 格式

### 翻译贡献
我们欢迎多语言翻译：
1. 检查 [翻译状态](https://github.com/your-username/desktop-pet/wiki/Translation-Status)
2. 选择需要翻译的语言
3. 使用翻译工具或直接编辑文件
4. 提交 PR 包含翻译内容

## 设计贡献

### 设计资源
- **图标**: SVG 格式，支持多种尺寸
- **动画**: 使用 Lottie 或 CSS 动画
- **界面**: 提供 Figma 或 Sketch 文件

### 设计规范
- 遵循现有的设计语言
- 考虑可访问性要求
- 提供多种主题支持
- 优化性能和文件大小

## 社区参与

### 讨论和交流
- [GitHub Discussions](https://github.com/your-username/desktop-pet/discussions)
- [Discord 服务器](https://discord.gg/desktop-pet)
- [QQ 群](123456789)

### 活动和会议
- 定期的开发者会议
- 社区活动和比赛
- 技术分享和教程

## 认可和奖励

### 贡献者认可
- 在 README 中列出贡献者
- 发布说明中感谢贡献者
- 特殊贡献者获得徽章

### 奖励计划
- 优秀贡献者可获得纪念品
- 重大贡献者可成为项目维护者
- 定期举办贡献者聚会

## 获取帮助

如果您在贡献过程中遇到问题：

1. 查看 [FAQ](https://github.com/your-username/desktop-pet/wiki/FAQ)
2. 搜索现有的 Issues 和 Discussions
3. 在 [Discussions](https://github.com/your-username/desktop-pet/discussions) 中提问
4. 联系维护者：<EMAIL>

## 许可证

通过贡献代码，您同意您的贡献将在与项目相同的 [MIT 许可证](../LICENSE) 下发布。

---

再次感谢您的贡献！每一个贡献都让桌面宠物变得更好。🐾
