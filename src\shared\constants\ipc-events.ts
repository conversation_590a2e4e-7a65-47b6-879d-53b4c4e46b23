export const IPCEvents = {
  // 窗口管理
  WINDOW_SET_POSITION: 'window:set-position',
  WINDOW_GET_POSITION: 'window:get-position',
  WINDOW_SET_SIZE: 'window:set-size',
  WINDOW_GET_SIZE: 'window:get-size',
  WINDOW_SET_ALWAYS_ON_TOP: 'window:set-always-on-top',
  WINDOW_SHOW: 'window:show',
  WINDOW_HIDE: 'window:hide',
  WINDOW_TOGGLE: 'window:toggle',
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_RESTORE: 'window:restore',
  WINDOW_CLOSE: 'window:close',
  WINDOW_FOCUS: 'window:focus',
  WINDOW_BLUR: 'window:blur',

  // 应用控制
  APP_QUIT: 'app:quit',
  APP_RESTART: 'app:restart',
  APP_GET_VERSION: 'app:get-version',
  APP_GET_PLATFORM: 'app:get-platform',
  APP_GET_PATH: 'app:get-path',
  APP_SET_AUTO_START: 'app:set-auto-start',
  APP_GET_AUTO_START: 'app:get-auto-start',

  // 系统信息
  SYSTEM_GET_SCREEN_SIZE: 'system:get-screen-size',
  SYSTEM_GET_CURSOR_POSITION: 'system:get-cursor-position',
  SYSTEM_GET_IDLE_TIME: 'system:get-idle-time',
  SYSTEM_GET_THEME: 'system:get-theme',
  SYSTEM_THEME_CHANGED: 'system:theme-changed',
  SYSTEM_POWER_STATE_CHANGED: 'system:power-state-changed',

  // 设置管理
  SETTINGS_OPEN: 'settings:open',
  SETTINGS_CLOSE: 'settings:close',
  SETTINGS_GET: 'settings:get',
  SETTINGS_SET: 'settings:set',
  SETTINGS_RESET: 'settings:reset',
  SETTINGS_EXPORT: 'settings:export',
  SETTINGS_IMPORT: 'settings:import',

  // 托盘管理
  TRAY_UPDATE_ICON: 'tray:update-icon',
  TRAY_UPDATE_TOOLTIP: 'tray:update-tooltip',
  TRAY_SHOW_BALLOON: 'tray:show-balloon',
  TRAY_CLICK: 'tray:click',
  TRAY_DOUBLE_CLICK: 'tray:double-click',
  TRAY_RIGHT_CLICK: 'tray:right-click',

  // 宠物管理
  PET_CREATE: 'pet:create',
  PET_DELETE: 'pet:delete',
  PET_SELECT: 'pet:select',
  PET_UPDATE_STATE: 'pet:update-state',
  PET_GET_STATE: 'pet:get-state',
  PET_SAVE_STATE: 'pet:save-state',
  PET_LOAD_STATE: 'pet:load-state',
  PET_RESET_STATE: 'pet:reset-state',

  // 宠物交互
  PET_FEED: 'pet:feed',
  PET_PLAY: 'pet:play',
  PET_CLEAN: 'pet:clean',
  PET_PET: 'pet:pet',
  PET_CLICK: 'pet:click',
  PET_DOUBLE_CLICK: 'pet:double-click',
  PET_RIGHT_CLICK: 'pet:right-click',
  PET_DRAG_START: 'pet:drag-start',
  PET_DRAG_END: 'pet:drag-end',

  // 动画控制
  ANIMATION_PLAY: 'animation:play',
  ANIMATION_STOP: 'animation:stop',
  ANIMATION_PAUSE: 'animation:pause',
  ANIMATION_RESUME: 'animation:resume',
  ANIMATION_GET_CURRENT: 'animation:get-current',
  ANIMATION_LOAD: 'animation:load',

  // 行为系统
  BEHAVIOR_START_AUTO: 'behavior:start-auto',
  BEHAVIOR_STOP_AUTO: 'behavior:stop-auto',
  BEHAVIOR_EXECUTE: 'behavior:execute',
  BEHAVIOR_GET_AVAILABLE: 'behavior:get-available',
  BEHAVIOR_REGISTER: 'behavior:register',
  BEHAVIOR_UNREGISTER: 'behavior:unregister',

  // 文件系统
  FS_READ_FILE: 'fs:read-file',
  FS_WRITE_FILE: 'fs:write-file',
  FS_DELETE_FILE: 'fs:delete-file',
  FS_EXISTS: 'fs:exists',
  FS_CREATE_DIR: 'fs:create-dir',
  FS_READ_DIR: 'fs:read-dir',

  // 通知系统
  NOTIFICATION_SHOW: 'notification:show',
  NOTIFICATION_CLOSE: 'notification:close',
  NOTIFICATION_CLICK: 'notification:click',

  // 音频系统
  AUDIO_PLAY: 'audio:play',
  AUDIO_STOP: 'audio:stop',
  AUDIO_PAUSE: 'audio:pause',
  AUDIO_RESUME: 'audio:resume',
  AUDIO_SET_VOLUME: 'audio:set-volume',
  AUDIO_GET_VOLUME: 'audio:get-volume',

  // 更新系统
  UPDATE_CHECK: 'update:check',
  UPDATE_DOWNLOAD: 'update:download',
  UPDATE_INSTALL: 'update:install',
  UPDATE_AVAILABLE: 'update:available',
  UPDATE_DOWNLOADED: 'update:downloaded',
  UPDATE_ERROR: 'update:error',

  // 日志系统
  LOG_INFO: 'log:info',
  LOG_WARN: 'log:warn',
  LOG_ERROR: 'log:error',
  LOG_DEBUG: 'log:debug',

  // 统计分析
  ANALYTICS_TRACK: 'analytics:track',
  ANALYTICS_IDENTIFY: 'analytics:identify',
  ANALYTICS_PAGE: 'analytics:page',

  // 错误报告
  ERROR_REPORT: 'error:report',
  ERROR_HANDLED: 'error:handled',

  // 开发工具
  DEV_RELOAD: 'dev:reload',
  DEV_TOGGLE_DEVTOOLS: 'dev:toggle-devtools',
  DEV_CLEAR_CACHE: 'dev:clear-cache',
} as const;

export type IPCEventType = typeof IPCEvents[keyof typeof IPCEvents];
