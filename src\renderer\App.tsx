import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PetWidget } from './components/Pet/PetWidget';
import { StatusBar } from './components/UI/StatusBar';
import { ContextMenu } from './components/UI/ContextMenu';
import { SettingsModal } from './components/Settings/SettingsModal';
import { usePetStore } from './stores/petStore';
import { useSettingsStore } from './stores/settingsStore';
import { useAppStore } from './stores/appStore';
import { logger } from '@shared/utils/logger';
import { clsx } from 'clsx';

export const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  // 状态管理
  const { currentPet, initializePet, updatePetStats } = usePetStore();
  const { settings, loadSettings } = useSettingsStore();
  const { isSettingsOpen, setSettingsOpen } = useAppStore();

  // 初始化应用
  useEffect(() => {
    const initializeApp = async () => {
      try {
        logger.info('Initializing Desktop Pet App...');

        // 加载设置
        await loadSettings();

        // 初始化宠物
        await initializePet();

        // 设置定时器更新宠物状态
        const statsUpdateInterval = setInterval(() => {
          updatePetStats();
        }, 60000); // 每分钟更新一次

        setIsLoading(false);
        logger.info('Desktop Pet App initialized successfully');

        // 清理函数
        return () => {
          clearInterval(statsUpdateInterval);
        };
      } catch (error) {
        logger.error('Failed to initialize app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, [initializePet, loadSettings, updatePetStats]);

  // 处理右键菜单
  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      visible: true
    });
  };

  // 关闭右键菜单
  const handleCloseContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + S: 打开设置
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        setSettingsOpen(true);
      }

      // Ctrl/Cmd + H: 隐藏/显示宠物
      if ((event.ctrlKey || event.metaKey) && event.key === 'h') {
        event.preventDefault();
        // 通过IPC通知主进程切换窗口可见性
        window.electronAPI?.toggleWindow();
      }

      // F1: 显示帮助
      if (event.key === 'F1') {
        event.preventDefault();
        // 显示帮助信息
        logger.info('Help requested');
      }

      // Escape: 关闭模态框
      if (event.key === 'Escape') {
        if (isSettingsOpen) {
          setSettingsOpen(false);
        }
        if (contextMenu.visible) {
          handleCloseContextMenu();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isSettingsOpen, contextMenu.visible, setSettingsOpen]);

  // 处理窗口点击（关闭右键菜单）
  useEffect(() => {
    const handleClick = () => {
      if (contextMenu.visible) {
        handleCloseContextMenu();
      }
    };

    window.addEventListener('click', handleClick);
    return () => window.removeEventListener('click', handleClick);
  }, [contextMenu.visible]);

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <motion.div
          className="w-8 h-8 border-2 border-pink-500 border-t-transparent rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
      </div>
    );
  }

  // 错误状态
  if (!currentPet) {
    return (
      <div className="flex flex-col items-center justify-center w-full h-full p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="text-4xl mb-4">😿</div>
          <div className="text-sm text-gray-600">
            宠物加载失败
            <br />
            请重启应用
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div 
      className={clsx(
        "relative w-full h-full overflow-hidden",
        "bg-transparent",
        settings.theme === 'dark' && "dark"
      )}
      onContextMenu={handleContextMenu}
    >
      {/* 主要宠物组件 */}
      <PetWidget />

      {/* 状态栏 */}
      <AnimatePresence>
        {settings.showStatusBar && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute top-2 left-2 right-2"
          >
            <StatusBar />
          </motion.div>
        )}
      </AnimatePresence>

      {/* 右键菜单 */}
      <AnimatePresence>
        {contextMenu.visible && (
          <ContextMenu
            x={contextMenu.x}
            y={contextMenu.y}
            onClose={handleCloseContextMenu}
          />
        )}
      </AnimatePresence>

      {/* 设置模态框 */}
      <AnimatePresence>
        {isSettingsOpen && (
          <SettingsModal onClose={() => setSettingsOpen(false)} />
        )}
      </AnimatePresence>

      {/* 开发模式指示器 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-2 right-2 px-2 py-1 bg-red-500 text-white text-xs rounded">
          DEV
        </div>
      )}
    </div>
  );
};
