import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { logger } from '@shared/utils/logger';

export interface SettingsState {
  // 应用设置
  autoStart: boolean;
  alwaysOnTop: boolean;
  showInTaskbar: boolean;
  minimizeToTray: boolean;
  
  // 宠物设置
  petSize: number; // 0.5-2.0
  animationSpeed: number; // 0.5-2.0
  enableSounds: boolean;
  soundVolume: number; // 0-100
  showPetName: boolean;
  
  // 行为设置
  enableAutoBehavior: boolean;
  behaviorFrequency: number; // 分钟
  enableIdleMode: boolean;
  idleTimeout: number; // 分钟
  
  // 外观设置
  theme: 'light' | 'dark' | 'auto';
  transparency: number; // 0-100
  showStatusBar: boolean;
  showStats: boolean;
  
  // 通知设置
  enableNotifications: boolean;
  notifyOnLowStats: boolean;
  notifyOnMilestones: boolean;
  
  // 高级设置
  enableAnalytics: boolean;
  enableCrashReporting: boolean;
  debugMode: boolean;
}

interface SettingsStore {
  settings: SettingsState;
  isLoading: boolean;
  error: string | null;
  
  // 动作
  loadSettings: () => Promise<void>;
  updateSettings: (newSettings: Partial<SettingsState>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (data: string) => boolean;
  
  // 特定设置更新
  setTheme: (theme: SettingsState['theme']) => void;
  setPetSize: (size: number) => void;
  setVolume: (volume: number) => void;
  toggleAlwaysOnTop: () => void;
  toggleSounds: () => void;
  toggleNotifications: () => void;
}

const defaultSettings: SettingsState = {
  // 应用设置
  autoStart: false,
  alwaysOnTop: true,
  showInTaskbar: false,
  minimizeToTray: true,
  
  // 宠物设置
  petSize: 1.0,
  animationSpeed: 1.0,
  enableSounds: true,
  soundVolume: 50,
  showPetName: true,
  
  // 行为设置
  enableAutoBehavior: true,
  behaviorFrequency: 5,
  enableIdleMode: true,
  idleTimeout: 30,
  
  // 外观设置
  theme: 'auto',
  transparency: 0,
  showStatusBar: false,
  showStats: true,
  
  // 通知设置
  enableNotifications: true,
  notifyOnLowStats: true,
  notifyOnMilestones: true,
  
  // 高级设置
  enableAnalytics: false,
  enableCrashReporting: true,
  debugMode: process.env.NODE_ENV === 'development'
};

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // 加载设置
      loadSettings: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // 设置会通过persist中间件自动加载
          // 这里可以添加额外的验证或迁移逻辑
          
          const { settings } = get();
          
          // 应用设置到主进程
          if (window.electronAPI) {
            await window.electronAPI.setAlwaysOnTop(settings.alwaysOnTop);
            await window.electronAPI.setAutoStart(settings.autoStart);
          }
          
          set({ isLoading: false });
          logger.info('Settings loaded successfully');
        } catch (error) {
          logger.error('Failed to load settings:', error);
          set({ 
            error: 'Failed to load settings',
            isLoading: false 
          });
        }
      },

      // 更新设置
      updateSettings: (newSettings: Partial<SettingsState>) => {
        const { settings } = get();
        const updatedSettings = { ...settings, ...newSettings };
        
        set({ settings: updatedSettings });
        
        // 应用某些设置到主进程
        if (window.electronAPI) {
          if ('alwaysOnTop' in newSettings) {
            window.electronAPI.setAlwaysOnTop(newSettings.alwaysOnTop!);
          }
          if ('autoStart' in newSettings) {
            window.electronAPI.setAutoStart(newSettings.autoStart!);
          }
        }
        
        logger.info('Settings updated:', newSettings);
      },

      // 重置设置
      resetSettings: () => {
        set({ settings: defaultSettings });
        logger.info('Settings reset to defaults');
      },

      // 导出设置
      exportSettings: () => {
        const { settings } = get();
        const exportData = {
          version: '1.0.0',
          timestamp: Date.now(),
          settings
        };
        
        return JSON.stringify(exportData, null, 2);
      },

      // 导入设置
      importSettings: (data: string) => {
        try {
          const importData = JSON.parse(data);
          
          // 验证数据格式
          if (!importData.settings || typeof importData.settings !== 'object') {
            throw new Error('Invalid settings format');
          }
          
          // 合并设置（保留默认值）
          const newSettings = { ...defaultSettings, ...importData.settings };
          
          set({ settings: newSettings });
          logger.info('Settings imported successfully');
          return true;
        } catch (error) {
          logger.error('Failed to import settings:', error);
          set({ error: 'Failed to import settings' });
          return false;
        }
      },

      // 设置主题
      setTheme: (theme: SettingsState['theme']) => {
        const { updateSettings } = get();
        updateSettings({ theme });
        
        // 应用主题到DOM
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else if (theme === 'light') {
          root.classList.remove('dark');
        } else {
          // 自动主题：根据系统设置
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (prefersDark) {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      // 设置宠物大小
      setPetSize: (size: number) => {
        const clampedSize = Math.max(0.5, Math.min(2.0, size));
        const { updateSettings } = get();
        updateSettings({ petSize: clampedSize });
      },

      // 设置音量
      setVolume: (volume: number) => {
        const clampedVolume = Math.max(0, Math.min(100, volume));
        const { updateSettings } = get();
        updateSettings({ soundVolume: clampedVolume });
      },

      // 切换置顶
      toggleAlwaysOnTop: () => {
        const { settings, updateSettings } = get();
        updateSettings({ alwaysOnTop: !settings.alwaysOnTop });
      },

      // 切换音效
      toggleSounds: () => {
        const { settings, updateSettings } = get();
        updateSettings({ enableSounds: !settings.enableSounds });
      },

      // 切换通知
      toggleNotifications: () => {
        const { settings, updateSettings } = get();
        updateSettings({ enableNotifications: !settings.enableNotifications });
      }
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        settings: state.settings
      }),
      // 版本迁移
      version: 1,
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          // 从版本0迁移到版本1
          return {
            ...persistedState,
            settings: {
              ...defaultSettings,
              ...persistedState.settings
            }
          };
        }
        return persistedState;
      }
    }
  )
);

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  mediaQuery.addEventListener('change', (e) => {
    const { settings, setTheme } = useSettingsStore.getState();
    if (settings.theme === 'auto') {
      setTheme('auto'); // 重新应用自动主题
    }
  });
}
