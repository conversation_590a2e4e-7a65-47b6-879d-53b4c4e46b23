import { app, BrowserWindow, ipcMain, screen, shell } from 'electron';
import { join } from 'path';
import { WindowManager } from './window/window-manager';
import { TrayManager } from './tray/tray-manager';
import { IPCHandler } from './ipc/ipc-handler';
import { AppUpdater } from './updater/app-updater';
import { logger } from '@shared/utils/logger';

class DesktopPetApp {
  private windowManager: WindowManager;
  private trayManager: TrayManager;
  private ipcHandler: IPCHandler;
  private appUpdater: AppUpdater;

  constructor() {
    this.windowManager = new WindowManager();
    this.trayManager = new TrayManager();
    this.ipcHandler = new IPCHandler();
    this.appUpdater = new AppUpdater();

    this.initializeApp();
  }

  private initializeApp(): void {
    // 设置应用用户模型ID (Windows)
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.example.desktop-pet');
    }

    // 禁用硬件加速 (可选，解决某些显卡兼容性问题)
    // app.disableHardwareAcceleration();

    // 应用事件监听
    app.whenReady().then(() => this.onAppReady());
    app.on('window-all-closed', this.onWindowAllClosed);
    app.on('activate', this.onActivate);
    app.on('before-quit', this.onBeforeQuit);
    app.on('web-contents-created', this.onWebContentsCreated);

    // 安全设置
    app.on('web-contents-created', (_, contents) => {
      contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      });
    });

    // 处理协议链接 (可选)
    app.setAsDefaultProtocolClient('desktop-pet');
  }

  private async onAppReady(): Promise<void> {
    try {
      logger.info('App is ready, initializing components...');

      // 初始化各个管理器
      await this.windowManager.initialize();
      await this.trayManager.initialize();
      this.ipcHandler.initialize();

      // 创建主窗口
      await this.windowManager.createMainWindow();

      // 检查更新
      if (process.env.NODE_ENV === 'production') {
        this.appUpdater.checkForUpdates();
      }

      logger.info('Desktop Pet application started successfully');
    } catch (error) {
      logger.error('Failed to initialize app:', error);
      app.quit();
    }
  }

  private onWindowAllClosed = (): void => {
    // 在 macOS 上，应用通常保持活跃状态，即使没有打开的窗口
    if (process.platform !== 'darwin') {
      app.quit();
    }
  };

  private onActivate = async (): Promise<void> => {
    // 在 macOS 上，当点击 dock 图标且没有其他窗口打开时，
    // 通常会重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) {
      await this.windowManager.createMainWindow();
    } else {
      this.windowManager.showMainWindow();
    }
  };

  private onBeforeQuit = (): void => {
    logger.info('App is quitting...');
    this.windowManager.cleanup();
    this.trayManager.cleanup();
  };

  private onWebContentsCreated = (_, contents: Electron.WebContents): void => {
    // 安全设置：阻止新窗口创建
    contents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // 阻止导航到外部URL
    contents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);
      
      if (parsedUrl.origin !== 'http://localhost:3000' && 
          parsedUrl.origin !== 'file://') {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      }
    });
  };
}

// 确保只有一个应用实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，聚焦到主窗口
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });

  // 创建应用实例
  new DesktopPetApp();
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
