export type PetType = 'cat' | 'dog' | 'rabbit' | 'bird';

export type PetMood = 'happy' | 'sad' | 'angry' | 'sleepy' | 'excited' | 'neutral';

export type PetAnimation = 
  | 'idle' 
  | 'walk' 
  | 'run' 
  | 'jump' 
  | 'sleep' 
  | 'eat' 
  | 'play' 
  | 'clean' 
  | 'happy' 
  | 'sad' 
  | 'angry';

export interface PetStats {
  hunger: number;      // 0-100
  happiness: number;   // 0-100
  health: number;      // 0-100
  energy: number;      // 0-100
  cleanliness: number; // 0-100
}

export interface PetState {
  id: string;
  name: string;
  type: PetType;
  level: number;
  experience: number;
  stats: PetStats;
  position: { x: number; y: number };
  size: number; // 缩放比例 0.5-2.0
  currentAnimation: PetAnimation;
  mood: PetMood;
  isVisible: boolean;
  lastFeedTime: number;
  lastPlayTime: number;
  lastCleanTime: number;
  birthTime: number;
  totalPlayTime: number;
}

export interface PetConfig {
  name: string;
  type: PetType;
  initialPosition?: { x: number; y: number };
  size?: number;
}

export interface PetBehavior {
  id: string;
  name: string;
  priority: number;
  conditions: BehaviorCondition[];
  actions: BehaviorAction[];
  cooldown: number; // 毫秒
  lastExecuted?: number;
}

export interface BehaviorCondition {
  type: 'stat' | 'time' | 'random' | 'interaction' | 'mood';
  parameter: string;
  operator: '>' | '<' | '=' | '>=' | '<=' | '!=';
  value: number | string;
}

export interface BehaviorAction {
  type: 'animation' | 'move' | 'stat_change' | 'sound' | 'notification' | 'mood_change';
  parameters: Record<string, any>;
  duration?: number;
}

export interface PetInteraction {
  type: 'click' | 'double_click' | 'right_click' | 'drag' | 'feed' | 'play' | 'clean';
  position: { x: number; y: number };
  timestamp: number;
  data?: any;
}

export interface PetEvent {
  type: string;
  petId: string;
  timestamp: number;
  data?: any;
}

export interface AnimationFrame {
  id: string;
  duration: number;
  spriteX: number;
  spriteY: number;
  width: number;
  height: number;
  offsetX?: number;
  offsetY?: number;
}

export interface Animation {
  id: string;
  name: PetAnimation;
  frames: AnimationFrame[];
  loop: boolean;
  speed: number;
  spriteSheet: string;
}

export interface PetAssets {
  spriteSheets: Record<string, string>;
  animations: Record<PetAnimation, Animation>;
  sounds: Record<string, string>;
}

export interface PetTypeConfig {
  type: PetType;
  displayName: string;
  description: string;
  assets: PetAssets;
  defaultStats: PetStats;
  behaviors: PetBehavior[];
  specialAbilities?: string[];
}

// 宠物成长相关
export interface LevelInfo {
  level: number;
  requiredExperience: number;
  rewards: LevelReward[];
}

export interface LevelReward {
  type: 'animation' | 'behavior' | 'stat_boost' | 'ability';
  value: string | number;
  description: string;
}

// 宠物成就系统
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: AchievementCondition;
  reward?: AchievementReward;
  unlocked: boolean;
  unlockedAt?: number;
}

export interface AchievementCondition {
  type: 'stat_reach' | 'time_played' | 'interactions' | 'level_reach' | 'special_event';
  parameter: string;
  value: number;
}

export interface AchievementReward {
  type: 'experience' | 'stat_boost' | 'unlock_content';
  value: number | string;
}

// 宠物物品系统
export interface PetItem {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'food' | 'toy' | 'medicine' | 'accessory';
  effects: ItemEffect[];
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  price?: number;
}

export interface ItemEffect {
  type: 'stat_change' | 'mood_change' | 'animation' | 'temporary_boost';
  parameter: string;
  value: number;
  duration?: number; // 毫秒，用于临时效果
}
