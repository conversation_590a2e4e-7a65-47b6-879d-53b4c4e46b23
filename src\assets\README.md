# 资源文件说明

这个目录包含了桌面宠物应用的所有静态资源文件。

## 目录结构

```
assets/
├── icons/              # 应用图标
│   ├── icon.ico        # Windows 图标
│   ├── icon.icns       # macOS 图标
│   ├── icon.png        # Linux 图标
│   └── tray-*.png      # 托盘图标
├── sprites/            # 宠物精灵图
│   ├── cat/            # 猫咪精灵
│   ├── dog/            # 狗狗精灵
│   ├── rabbit/         # 兔子精灵
│   └── bird/           # 小鸟精灵
├── sounds/             # 音效文件
│   ├── click.wav       # 点击音效
│   ├── feed.wav        # 喂食音效
│   ├── play.wav        # 玩耍音效
│   └── clean.wav       # 清洁音效
└── animations/         # 动画配置
    ├── cat.json        # 猫咪动画
    ├── dog.json        # 狗狗动画
    ├── rabbit.json     # 兔子动画
    └── bird.json       # 小鸟动画
```

## 图标规格

### 应用图标
- **Windows**: 256x256 ICO 格式
- **macOS**: 512x512 ICNS 格式
- **Linux**: 512x512 PNG 格式

### 托盘图标
- **尺寸**: 16x16, 32x32
- **格式**: PNG (支持透明)
- **状态**: normal, notification, sleeping, happy, sad

## 精灵图规格

### 图片格式
- **格式**: PNG (支持透明)
- **尺寸**: 建议 64x64 像素每帧
- **排列**: 水平排列的精灵表

### 动画帧
每个宠物类型需要以下动画：
- `idle`: 待机动画 (4-6帧)
- `walk`: 行走动画 (4-6帧)
- `run`: 跑步动画 (4-6帧)
- `jump`: 跳跃动画 (3-4帧)
- `sleep`: 睡眠动画 (2-3帧)
- `eat`: 进食动画 (3-4帧)
- `play`: 玩耍动画 (4-6帧)
- `clean`: 清洁动画 (3-4帧)
- `happy`: 开心动画 (3-4帧)
- `sad`: 难过动画 (2-3帧)
- `angry`: 生气动画 (2-3帧)

## 音效规格

### 音频格式
- **格式**: WAV 或 OGG
- **采样率**: 44.1kHz
- **位深**: 16-bit
- **声道**: 单声道或立体声
- **时长**: 建议 0.5-2 秒

### 音效类型
- `click`: 点击宠物时的音效
- `feed`: 喂食时的音效
- `play`: 玩耍时的音效
- `clean`: 清洁时的音效
- `level_up`: 升级时的音效
- `notification`: 通知音效

## 动画配置

动画配置文件使用 JSON 格式，定义每个动画的帧序列和时长：

```json
{
  "idle": {
    "frames": [
      { "x": 0, "y": 0, "width": 64, "height": 64, "duration": 500 },
      { "x": 64, "y": 0, "width": 64, "height": 64, "duration": 500 },
      { "x": 128, "y": 0, "width": 64, "height": 64, "duration": 500 },
      { "x": 192, "y": 0, "width": 64, "height": 64, "duration": 500 }
    ],
    "loop": true,
    "speed": 1.0
  }
}
```

## 资源优化

### 图片优化
- 使用 PNG 压缩工具减小文件大小
- 移除不必要的元数据
- 合理使用透明通道

### 音频优化
- 使用音频压缩减小文件大小
- 移除静音部分
- 标准化音量

## 版权说明

所有资源文件应确保：
- 拥有合法使用权
- 符合开源许可证要求
- 注明原作者和来源（如适用）

## 贡献资源

如果您想为项目贡献资源文件：
1. 确保资源符合上述规格要求
2. 提供资源的版权信息
3. 通过 Pull Request 提交
4. 在提交信息中说明资源用途
