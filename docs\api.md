# API 文档

## 概述

本文档描述了桌面宠物应用的内部 API 接口，包括主进程与渲染进程之间的通信接口、状态管理接口和核心业务逻辑接口。

## IPC 通信接口

### 主进程 → 渲染进程

#### 窗口管理
```typescript
// 窗口状态变化
interface WindowStateChanged {
  type: 'window-state-changed';
  payload: {
    isVisible: boolean;
    isMinimized: boolean;
    bounds: { x: number; y: number; width: number; height: number };
  };
}

// 系统主题变化
interface SystemThemeChanged {
  type: 'system-theme-changed';
  payload: {
    theme: 'light' | 'dark';
  };
}
```

#### 系统事件
```typescript
// 系统空闲状态
interface SystemIdleChanged {
  type: 'system-idle-changed';
  payload: {
    isIdle: boolean;
    idleTime: number; // 秒
  };
}

// 电源状态变化
interface PowerStateChanged {
  type: 'power-state-changed';
  payload: {
    isOnBattery: boolean;
    batteryLevel: number; // 0-100
  };
}
```

### 渲染进程 → 主进程

#### 窗口控制
```typescript
// 设置窗口位置
interface SetWindowPosition {
  type: 'set-window-position';
  payload: {
    x: number;
    y: number;
  };
}

// 设置窗口大小
interface SetWindowSize {
  type: 'set-window-size';
  payload: {
    width: number;
    height: number;
  };
}

// 设置窗口置顶
interface SetAlwaysOnTop {
  type: 'set-always-on-top';
  payload: {
    alwaysOnTop: boolean;
  };
}
```

#### 应用控制
```typescript
// 退出应用
interface QuitApp {
  type: 'quit-app';
}

// 重启应用
interface RestartApp {
  type: 'restart-app';
}

// 显示/隐藏应用
interface ToggleVisibility {
  type: 'toggle-visibility';
}
```

## 状态管理 API

### Pet Store

```typescript
interface PetState {
  // 基础属性
  id: string;
  name: string;
  type: 'cat' | 'dog' | 'rabbit' | 'bird';
  level: number;
  experience: number;
  
  // 状态值 (0-100)
  hunger: number;
  happiness: number;
  health: number;
  energy: number;
  cleanliness: number;
  
  // 位置和外观
  position: { x: number; y: number };
  size: number; // 缩放比例
  currentAnimation: string;
  mood: 'happy' | 'sad' | 'angry' | 'sleepy' | 'excited';
  
  // 时间相关
  birthTime: number;
  lastFeedTime: number;
  lastPlayTime: number;
  lastCleanTime: number;
}

interface PetActions {
  // 基础操作
  createPet: (config: PetConfig) => void;
  deletePet: (id: string) => void;
  selectPet: (id: string) => void;
  
  // 状态更新
  updatePosition: (position: { x: number; y: number }) => void;
  updateStats: (stats: Partial<PetStats>) => void;
  updateMood: (mood: PetState['mood']) => void;
  
  // 交互操作
  feed: (foodType?: string) => void;
  play: (gameType?: string) => void;
  clean: () => void;
  pet: () => void; // 抚摸
  
  // 动画控制
  playAnimation: (animationName: string) => void;
  stopAnimation: () => void;
  
  // 自动行为
  startAutoBehavior: () => void;
  stopAutoBehavior: () => void;
}
```

### Settings Store

```typescript
interface SettingsState {
  // 应用设置
  autoStart: boolean;
  alwaysOnTop: boolean;
  showInTaskbar: boolean;
  minimizeToTray: boolean;
  
  // 宠物设置
  petSize: number; // 0.5-2.0
  animationSpeed: number; // 0.5-2.0
  enableSounds: boolean;
  soundVolume: number; // 0-100
  
  // 行为设置
  enableAutoBehavior: boolean;
  behaviorFrequency: number; // 分钟
  enableIdleMode: boolean;
  idleTimeout: number; // 分钟
  
  // 外观设置
  theme: 'light' | 'dark' | 'auto';
  transparency: number; // 0-100
  showStatusBar: boolean;
  showStats: boolean;
  
  // 通知设置
  enableNotifications: boolean;
  notifyOnLowStats: boolean;
  notifyOnMilestones: boolean;
}

interface SettingsActions {
  updateSettings: (settings: Partial<SettingsState>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (data: string) => void;
}
```

## 核心业务逻辑 API

### Pet Controller

```typescript
class PetController {
  // 生命周期管理
  initialize(): Promise<void>;
  destroy(): void;
  
  // 状态管理
  updateStats(deltaTime: number): void;
  calculateMood(): PetState['mood'];
  checkLevelUp(): boolean;
  
  // 行为控制
  executeRandomBehavior(): void;
  executeBehavior(behaviorName: string): void;
  canExecuteBehavior(behaviorName: string): boolean;
  
  // 交互处理
  handleClick(position: { x: number; y: number }): void;
  handleDrag(startPos: { x: number; y: number }, endPos: { x: number; y: number }): void;
  handleDoubleClick(): void;
  handleRightClick(): void;
  
  // 事件监听
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
  emit(event: string, data?: any): void;
}
```

### Animation Engine

```typescript
interface AnimationFrame {
  id: string;
  duration: number;
  spriteX: number;
  spriteY: number;
  width: number;
  height: number;
}

interface Animation {
  id: string;
  name: string;
  frames: AnimationFrame[];
  loop: boolean;
  speed: number;
}

class AnimationEngine {
  // 动画管理
  loadAnimation(animation: Animation): void;
  playAnimation(animationId: string, options?: AnimationOptions): void;
  stopAnimation(): void;
  pauseAnimation(): void;
  resumeAnimation(): void;
  
  // 动画状态
  getCurrentFrame(): AnimationFrame | null;
  getProgress(): number; // 0-1
  isPlaying(): boolean;
  
  // 事件回调
  onAnimationStart(callback: () => void): void;
  onAnimationEnd(callback: () => void): void;
  onFrameChange(callback: (frame: AnimationFrame) => void): void;
}
```

### Behavior System

```typescript
interface BehaviorCondition {
  type: 'stat' | 'time' | 'random' | 'interaction';
  parameter: string;
  operator: '>' | '<' | '=' | '>=' | '<=';
  value: number | string;
}

interface Behavior {
  id: string;
  name: string;
  priority: number;
  conditions: BehaviorCondition[];
  actions: BehaviorAction[];
  cooldown: number; // 毫秒
}

interface BehaviorAction {
  type: 'animation' | 'move' | 'stat_change' | 'sound' | 'notification';
  parameters: Record<string, any>;
}

class BehaviorSystem {
  // 行为管理
  registerBehavior(behavior: Behavior): void;
  unregisterBehavior(behaviorId: string): void;
  
  // 行为执行
  evaluateBehaviors(): Behavior[];
  executeBehavior(behavior: Behavior): void;
  
  // 条件检查
  checkCondition(condition: BehaviorCondition): boolean;
  checkAllConditions(conditions: BehaviorCondition[]): boolean;
  
  // 状态管理
  isOnCooldown(behaviorId: string): boolean;
  setCooldown(behaviorId: string, duration: number): void;
}
```

## 工具函数 API

### 数学工具

```typescript
// 位置和几何计算
function clamp(value: number, min: number, max: number): number;
function lerp(start: number, end: number, factor: number): number;
function distance(p1: Point, p2: Point): number;
function randomInRange(min: number, max: number): number;

// 屏幕边界检查
function isInBounds(position: Point, bounds: Rectangle): boolean;
function constrainToBounds(position: Point, bounds: Rectangle): Point;
```

### 时间工具

```typescript
// 时间格式化
function formatDuration(milliseconds: number): string;
function formatTime(timestamp: number): string;

// 时间计算
function getTimeSince(timestamp: number): number;
function addTime(timestamp: number, duration: number): number;
```

### 存储工具

```typescript
// 本地存储
function saveData<T>(key: string, data: T): Promise<void>;
function loadData<T>(key: string): Promise<T | null>;
function deleteData(key: string): Promise<void>;

// 配置管理
function getConfig(path: string): any;
function setConfig(path: string, value: any): void;
function resetConfig(): void;
```

## 事件系统

### 全局事件

```typescript
// 宠物事件
'pet:created' | 'pet:deleted' | 'pet:selected'
'pet:fed' | 'pet:played' | 'pet:cleaned' | 'pet:petted'
'pet:level-up' | 'pet:mood-changed' | 'pet:stats-changed'

// 动画事件
'animation:started' | 'animation:ended' | 'animation:frame-changed'

// 行为事件
'behavior:executed' | 'behavior:failed' | 'behavior:cooldown-expired'

// 系统事件
'app:ready' | 'app:quit' | 'app:focus' | 'app:blur'
'window:moved' | 'window:resized' | 'window:minimized' | 'window:restored'

// 用户交互事件
'user:click' | 'user:double-click' | 'user:right-click' | 'user:drag'
'user:idle' | 'user:active'
```

### 事件数据结构

```typescript
interface PetEvent {
  type: string;
  petId: string;
  timestamp: number;
  data?: any;
}

interface UserInteractionEvent {
  type: string;
  position: { x: number; y: number };
  timestamp: number;
  modifiers?: {
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
  };
}
```

## 错误处理

### 错误类型

```typescript
class PetError extends Error {
  code: string;
  details?: any;
  
  constructor(message: string, code: string, details?: any) {
    super(message);
    this.code = code;
    this.details = details;
  }
}

// 错误代码
const ERROR_CODES = {
  PET_NOT_FOUND: 'PET_NOT_FOUND',
  ANIMATION_LOAD_FAILED: 'ANIMATION_LOAD_FAILED',
  SAVE_DATA_FAILED: 'SAVE_DATA_FAILED',
  INVALID_BEHAVIOR: 'INVALID_BEHAVIOR',
  WINDOW_CREATION_FAILED: 'WINDOW_CREATION_FAILED'
} as const;
```

### 错误处理器

```typescript
interface ErrorHandler {
  handleError(error: Error): void;
  reportError(error: Error): void;
  recoverFromError(error: Error): boolean;
}
```
