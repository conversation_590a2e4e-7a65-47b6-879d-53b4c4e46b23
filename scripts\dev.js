#!/usr/bin/env node

const { spawn } = require('child_process');
const { join } = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkDependencies() {
  log('检查依赖...', 'blue');
  
  const packageJsonPath = join(process.cwd(), 'package.json');
  const nodeModulesPath = join(process.cwd(), 'node_modules');
  
  if (!fs.existsSync(packageJsonPath)) {
    log('错误: 找不到 package.json 文件', 'red');
    process.exit(1);
  }
  
  if (!fs.existsSync(nodeModulesPath)) {
    log('错误: 找不到 node_modules 目录，请先运行 npm install', 'red');
    process.exit(1);
  }
  
  log('依赖检查通过', 'green');
}

function startRenderer() {
  log('启动渲染进程开发服务器...', 'cyan');
  
  const renderer = spawn('npm', ['run', 'dev:renderer'], {
    stdio: 'pipe',
    shell: true
  });
  
  renderer.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[渲染进程] ${output}`, 'cyan');
    }
  });
  
  renderer.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      log(`[渲染进程] ${output}`, 'yellow');
    }
  });
  
  return renderer;
}

function startMain() {
  log('启动主进程...', 'magenta');
  
  const main = spawn('npm', ['run', 'dev:main'], {
    stdio: 'pipe',
    shell: true
  });
  
  main.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      log(`[主进程] ${output}`, 'magenta');
    }
  });
  
  main.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('ExperimentalWarning')) {
      log(`[主进程] ${output}`, 'yellow');
    }
  });
  
  return main;
}

function cleanup(processes) {
  log('正在清理进程...', 'yellow');
  
  processes.forEach(proc => {
    if (proc && !proc.killed) {
      proc.kill('SIGTERM');
    }
  });
  
  setTimeout(() => {
    processes.forEach(proc => {
      if (proc && !proc.killed) {
        proc.kill('SIGKILL');
      }
    });
    process.exit(0);
  }, 3000);
}

async function main() {
  log('🐾 启动桌面宠物开发环境', 'green');
  log('================================', 'green');
  
  // 检查依赖
  checkDependencies();
  
  // 启动进程
  const rendererProcess = startRenderer();
  
  // 等待渲染进程启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const mainProcess = startMain();
  
  const processes = [rendererProcess, mainProcess];
  
  // 处理退出信号
  process.on('SIGINT', () => {
    log('\n收到退出信号，正在关闭...', 'yellow');
    cleanup(processes);
  });
  
  process.on('SIGTERM', () => {
    log('\n收到终止信号，正在关闭...', 'yellow');
    cleanup(processes);
  });
  
  // 监听进程退出
  rendererProcess.on('exit', (code) => {
    if (code !== 0) {
      log(`渲染进程异常退出，代码: ${code}`, 'red');
      cleanup(processes);
    }
  });
  
  mainProcess.on('exit', (code) => {
    if (code !== 0) {
      log(`主进程异常退出，代码: ${code}`, 'red');
      cleanup(processes);
    }
  });
  
  log('开发环境已启动！', 'green');
  log('按 Ctrl+C 退出', 'blue');
}

// 运行主函数
main().catch(error => {
  log(`启动失败: ${error.message}`, 'red');
  process.exit(1);
});
