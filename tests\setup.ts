import '@testing-library/jest-dom';

// Mock Electron APIs
const mockElectronAPI = {
  setWindowPosition: jest.fn(),
  getWindowPosition: jest.fn(),
  setWindowSize: jest.fn(),
  setAlwaysOnTop: jest.fn(),
  showWindow: jest.fn(),
  hideWindow: jest.fn(),
  toggleWindow: jest.fn(),
  quitApp: jest.fn(),
  restartApp: jest.fn(),
  getAppVersion: jest.fn().mockResolvedValue('1.0.0'),
  getPlatform: jest.fn().mockResolvedValue('win32'),
  setAutoStart: jest.fn(),
  getScreenSize: jest.fn().mockResolvedValue({ width: 1920, height: 1080 }),
  getCursorPosition: jest.fn().mockResolvedValue({ x: 100, y: 100 }),
  openSettings: jest.fn(),
  updateTrayIcon: jest.fn(),
  showBalloon: jest.fn(),
  updateTooltip: jest.fn(),
  readFile: jest.fn(),
  writeFile: jest.fn(),
  deleteFile: jest.fn(),
  showNotification: jest.fn(),
  reportError: jest.fn(),
  openExternal: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  once: jest.fn()
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock performance.memory
Object.defineProperty(performance, 'memory', {
  value: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000
  },
  writable: true
});

// Suppress console warnings in tests
const originalConsoleWarn = console.warn;
console.warn = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('React Router Future Flag Warning')
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Global test utilities
global.testUtils = {
  mockElectronAPI,
  localStorageMock
};
