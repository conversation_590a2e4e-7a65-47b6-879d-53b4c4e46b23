import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { PetState, PetStats, PetType, PetMood, PetAnimation } from '@shared/types/pet';
import { logger } from '@shared/utils/logger';

interface PetStore {
  // 状态
  currentPet: PetState | null;
  pets: PetState[];
  isLoading: boolean;
  error: string | null;

  // 动作
  initializePet: () => Promise<void>;
  createPet: (name: string, type: PetType) => Promise<void>;
  selectPet: (petId: string) => void;
  deletePet: (petId: string) => void;
  
  // 宠物状态更新
  updatePetStats: () => void;
  updatePetPosition: (position: { x: number; y: number }) => void;
  updatePetMood: (mood: PetMood) => void;
  updatePetAnimation: (animation: PetAnimation) => void;
  
  // 宠物交互
  feedPet: (foodType?: string) => void;
  playWithPet: (gameType?: string) => void;
  cleanPet: () => void;
  petPet: () => void;
  
  // 宠物行为
  startAutoBehavior: () => void;
  stopAutoBehavior: () => void;
  
  // 数据管理
  savePetData: () => Promise<void>;
  loadPetData: () => Promise<void>;
  resetPetData: () => void;
}

const createDefaultPet = (name: string, type: PetType): PetState => {
  const now = Date.now();
  
  return {
    id: `pet_${now}`,
    name,
    type,
    level: 1,
    experience: 0,
    stats: {
      hunger: 80,
      happiness: 80,
      health: 100,
      energy: 100,
      cleanliness: 90
    },
    position: { x: 100, y: 100 },
    size: 1.0,
    currentAnimation: 'idle',
    mood: 'happy',
    isVisible: true,
    lastFeedTime: now,
    lastPlayTime: now,
    lastCleanTime: now,
    birthTime: now,
    totalPlayTime: 0
  };
};

export const usePetStore = create<PetStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentPet: null,
      pets: [],
      isLoading: false,
      error: null,

      // 初始化宠物
      initializePet: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const { pets } = get();
          
          if (pets.length === 0) {
            // 创建默认宠物
            const defaultPet = createDefaultPet('小可爱', 'cat');
            set({ 
              pets: [defaultPet], 
              currentPet: defaultPet,
              isLoading: false 
            });
            logger.info('Created default pet');
          } else {
            // 选择第一个宠物
            set({ 
              currentPet: pets[0],
              isLoading: false 
            });
            logger.info('Loaded existing pet');
          }
        } catch (error) {
          logger.error('Failed to initialize pet:', error);
          set({ 
            error: 'Failed to initialize pet',
            isLoading: false 
          });
        }
      },

      // 创建新宠物
      createPet: async (name: string, type: PetType) => {
        try {
          const newPet = createDefaultPet(name, type);
          const { pets } = get();
          
          set({ 
            pets: [...pets, newPet],
            currentPet: newPet
          });
          
          logger.info('Created new pet:', { name, type });
        } catch (error) {
          logger.error('Failed to create pet:', error);
          set({ error: 'Failed to create pet' });
        }
      },

      // 选择宠物
      selectPet: (petId: string) => {
        const { pets } = get();
        const pet = pets.find(p => p.id === petId);
        
        if (pet) {
          set({ currentPet: pet });
          logger.info('Selected pet:', petId);
        }
      },

      // 删除宠物
      deletePet: (petId: string) => {
        const { pets, currentPet } = get();
        const updatedPets = pets.filter(p => p.id !== petId);
        
        set({ 
          pets: updatedPets,
          currentPet: currentPet?.id === petId ? 
            (updatedPets[0] || null) : currentPet
        });
        
        logger.info('Deleted pet:', petId);
      },

      // 更新宠物状态
      updatePetStats: () => {
        const { currentPet } = get();
        if (!currentPet) return;

        const now = Date.now();
        const timeSinceLastUpdate = now - (currentPet.lastFeedTime || now);
        const hoursElapsed = timeSinceLastUpdate / (1000 * 60 * 60);

        // 计算状态衰减
        const hungerDecay = Math.min(hoursElapsed * 2, 20);
        const energyDecay = Math.min(hoursElapsed * 1.5, 15);
        const cleanlinessDecay = Math.min(hoursElapsed * 1, 10);

        const newStats: PetStats = {
          hunger: Math.max(0, currentPet.stats.hunger - hungerDecay),
          happiness: Math.max(0, currentPet.stats.happiness - (hungerDecay * 0.5)),
          health: Math.max(0, currentPet.stats.health - (hungerDecay * 0.3)),
          energy: Math.max(0, currentPet.stats.energy - energyDecay),
          cleanliness: Math.max(0, currentPet.stats.cleanliness - cleanlinessDecay)
        };

        // 更新心情
        let newMood: PetMood = 'neutral';
        const avgStats = (newStats.hunger + newStats.happiness + newStats.health + newStats.energy) / 4;
        
        if (avgStats > 80) newMood = 'happy';
        else if (avgStats > 60) newMood = 'neutral';
        else if (avgStats > 40) newMood = 'sad';
        else if (avgStats > 20) newMood = 'angry';
        else newMood = 'sleepy';

        set({
          currentPet: {
            ...currentPet,
            stats: newStats,
            mood: newMood
          }
        });

        logger.debug('Updated pet stats:', newStats);
      },

      // 更新宠物位置
      updatePetPosition: (position: { x: number; y: number }) => {
        const { currentPet } = get();
        if (!currentPet) return;

        set({
          currentPet: {
            ...currentPet,
            position
          }
        });
      },

      // 更新宠物心情
      updatePetMood: (mood: PetMood) => {
        const { currentPet } = get();
        if (!currentPet) return;

        set({
          currentPet: {
            ...currentPet,
            mood
          }
        });
      },

      // 更新宠物动画
      updatePetAnimation: (animation: PetAnimation) => {
        const { currentPet } = get();
        if (!currentPet) return;

        set({
          currentPet: {
            ...currentPet,
            currentAnimation: animation
          }
        });
      },

      // 喂食宠物
      feedPet: (foodType = 'normal') => {
        const { currentPet } = get();
        if (!currentPet) return;

        const now = Date.now();
        const feedBonus = foodType === 'premium' ? 30 : 20;

        const newStats = {
          ...currentPet.stats,
          hunger: Math.min(100, currentPet.stats.hunger + feedBonus),
          happiness: Math.min(100, currentPet.stats.happiness + 10)
        };

        set({
          currentPet: {
            ...currentPet,
            stats: newStats,
            lastFeedTime: now,
            currentAnimation: 'eat'
          }
        });

        // 2秒后恢复待机动画
        setTimeout(() => {
          const { currentPet } = get();
          if (currentPet) {
            set({
              currentPet: {
                ...currentPet,
                currentAnimation: 'idle'
              }
            });
          }
        }, 2000);

        logger.info('Fed pet:', { foodType, newStats });
      },

      // 与宠物玩耍
      playWithPet: (gameType = 'normal') => {
        const { currentPet } = get();
        if (!currentPet) return;

        if (currentPet.stats.energy < 20) {
          logger.warn('Pet too tired to play');
          return;
        }

        const now = Date.now();
        const playBonus = gameType === 'special' ? 25 : 15;

        const newStats = {
          ...currentPet.stats,
          happiness: Math.min(100, currentPet.stats.happiness + playBonus),
          energy: Math.max(0, currentPet.stats.energy - 15)
        };

        set({
          currentPet: {
            ...currentPet,
            stats: newStats,
            lastPlayTime: now,
            totalPlayTime: currentPet.totalPlayTime + 1,
            currentAnimation: 'play'
          }
        });

        // 3秒后恢复待机动画
        setTimeout(() => {
          const { currentPet } = get();
          if (currentPet) {
            set({
              currentPet: {
                ...currentPet,
                currentAnimation: 'idle'
              }
            });
          }
        }, 3000);

        logger.info('Played with pet:', { gameType, newStats });
      },

      // 清洁宠物
      cleanPet: () => {
        const { currentPet } = get();
        if (!currentPet) return;

        const now = Date.now();
        const newStats = {
          ...currentPet.stats,
          cleanliness: 100,
          happiness: Math.min(100, currentPet.stats.happiness + 5)
        };

        set({
          currentPet: {
            ...currentPet,
            stats: newStats,
            lastCleanTime: now,
            currentAnimation: 'clean'
          }
        });

        // 2秒后恢复待机动画
        setTimeout(() => {
          const { currentPet } = get();
          if (currentPet) {
            set({
              currentPet: {
                ...currentPet,
                currentAnimation: 'idle'
              }
            });
          }
        }, 2000);

        logger.info('Cleaned pet:', newStats);
      },

      // 抚摸宠物
      petPet: () => {
        const { currentPet } = get();
        if (!currentPet) return;

        const newStats = {
          ...currentPet.stats,
          happiness: Math.min(100, currentPet.stats.happiness + 5)
        };

        set({
          currentPet: {
            ...currentPet,
            stats: newStats,
            currentAnimation: 'happy'
          }
        });

        // 1秒后恢复待机动画
        setTimeout(() => {
          const { currentPet } = get();
          if (currentPet) {
            set({
              currentPet: {
                ...currentPet,
                currentAnimation: 'idle'
              }
            });
          }
        }, 1000);

        logger.info('Pet petted:', newStats);
      },

      // 开始自动行为
      startAutoBehavior: () => {
        // TODO: 实现自动行为逻辑
        logger.info('Auto behavior started');
      },

      // 停止自动行为
      stopAutoBehavior: () => {
        // TODO: 实现停止自动行为逻辑
        logger.info('Auto behavior stopped');
      },

      // 保存宠物数据
      savePetData: async () => {
        try {
          // 数据会通过persist中间件自动保存
          logger.info('Pet data saved');
        } catch (error) {
          logger.error('Failed to save pet data:', error);
        }
      },

      // 加载宠物数据
      loadPetData: async () => {
        try {
          // 数据会通过persist中间件自动加载
          logger.info('Pet data loaded');
        } catch (error) {
          logger.error('Failed to load pet data:', error);
        }
      },

      // 重置宠物数据
      resetPetData: () => {
        set({
          currentPet: null,
          pets: [],
          isLoading: false,
          error: null
        });
        logger.info('Pet data reset');
      }
    }),
    {
      name: 'pet-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        pets: state.pets,
        currentPet: state.currentPet
      })
    }
  )
);
