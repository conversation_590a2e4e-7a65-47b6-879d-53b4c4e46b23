# 设计文档

## 设计理念

桌面宠物的设计理念是创造一个可爱、友好且不打扰用户工作的桌面伴侣。我们追求简洁而富有表现力的设计，让用户在繁忙的工作中也能感受到温暖和陪伴。

### 核心设计原则

1. **可爱至上** - 所有设计元素都应该传达可爱和友好的感觉
2. **非侵入性** - 不干扰用户的正常工作流程
3. **情感连接** - 通过动画和交互建立用户与宠物的情感纽带
4. **简洁明了** - 界面简洁，功能直观易懂
5. **个性化** - 支持用户自定义，满足不同喜好

## 视觉设计

### 色彩系统

#### 主色调
```css
/* 主要颜色 */
--primary-color: #FF6B9D;      /* 粉红色 - 主要强调色 */
--primary-light: #FFB3D1;     /* 浅粉色 - 悬停状态 */
--primary-dark: #E55A8A;      /* 深粉色 - 按下状态 */

/* 次要颜色 */
--secondary-color: #4ECDC4;   /* 青绿色 - 次要强调色 */
--secondary-light: #7EDDD6;   /* 浅青绿色 */
--secondary-dark: #3BA99F;    /* 深青绿色 */

/* 中性色 */
--neutral-100: #FFFFFF;       /* 纯白 */
--neutral-200: #F8F9FA;       /* 浅灰 */
--neutral-300: #E9ECEF;       /* 中浅灰 */
--neutral-400: #CED4DA;       /* 中灰 */
--neutral-500: #6C757D;       /* 深中灰 */
--neutral-600: #495057;       /* 深灰 */
--neutral-700: #343A40;       /* 很深灰 */
--neutral-800: #212529;       /* 近黑 */
--neutral-900: #000000;       /* 纯黑 */
```

#### 状态颜色
```css
/* 成功状态 */
--success-color: #28A745;     /* 绿色 */
--success-light: #D4EDDA;     /* 浅绿背景 */

/* 警告状态 */
--warning-color: #FFC107;     /* 黄色 */
--warning-light: #FFF3CD;     /* 浅黄背景 */

/* 错误状态 */
--error-color: #DC3545;       /* 红色 */
--error-light: #F8D7DA;       /* 浅红背景 */

/* 信息状态 */
--info-color: #17A2B8;        /* 蓝色 */
--info-light: #D1ECF1;        /* 浅蓝背景 */
```

#### 宠物状态颜色
```css
/* 饥饿状态 */
--hunger-high: #4CAF50;       /* 饱腹 - 绿色 */
--hunger-medium: #FF9800;     /* 轻微饥饿 - 橙色 */
--hunger-low: #F44336;        /* 饥饿 - 红色 */

/* 快乐状态 */
--happiness-high: #FFD700;    /* 开心 - 金色 */
--happiness-medium: #FFA500;  /* 一般 - 橙色 */
--happiness-low: #8B4513;     /* 难过 - 棕色 */

/* 健康状态 */
--health-high: #00FF7F;       /* 健康 - 春绿色 */
--health-medium: #FFFF00;     /* 一般 - 黄色 */
--health-low: #FF1493;        /* 不健康 - 深粉色 */

/* 能量状态 */
--energy-high: #00BFFF;       /* 充沛 - 深天蓝 */
--energy-medium: #87CEEB;     /* 一般 - 天蓝色 */
--energy-low: #4682B4;        /* 疲劳 - 钢蓝色 */
```

### 字体系统

#### 字体族
```css
/* 主要字体 */
--font-primary: 'Inter', 'Noto Sans SC', sans-serif;

/* 标题字体 */
--font-heading: 'Poppins', 'Noto Sans SC', sans-serif;

/* 等宽字体 */
--font-mono: 'JetBrains Mono', 'Consolas', monospace;

/* 装饰字体 */
--font-decorative: 'Fredoka One', 'Noto Sans SC', cursive;
```

#### 字体大小
```css
/* 字体大小比例 */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
--text-5xl: 3rem;      /* 48px */
```

#### 字重
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

### 间距系统

```css
/* 间距比例 (基于 4px) */
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
--space-20: 5rem;      /* 80px */
--space-24: 6rem;      /* 96px */
```

### 圆角系统

```css
/* 圆角大小 */
--radius-none: 0;
--radius-sm: 0.125rem;   /* 2px */
--radius-base: 0.25rem;  /* 4px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-2xl: 1rem;      /* 16px */
--radius-3xl: 1.5rem;    /* 24px */
--radius-full: 9999px;   /* 完全圆形 */
```

### 阴影系统

```css
/* 阴影效果 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
--shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

/* 特殊阴影 */
--shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
--shadow-outline: 0 0 0 3px rgba(66, 153, 225, 0.5);
--shadow-none: none;
```

## 组件设计

### 按钮设计

#### 主要按钮
```css
.btn-primary {
  background: var(--primary-color);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}
```

#### 次要按钮
```css
.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
}
```

### 卡片设计

```css
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-6);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}
```

### 状态指示器

```css
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.status-indicator--high {
  background: var(--success-light);
  color: var(--success-color);
}

.status-indicator--medium {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-indicator--low {
  background: var(--error-light);
  color: var(--error-color);
}
```

## 动画设计

### 动画原则

1. **自然流畅** - 模拟真实物理运动
2. **有意义** - 每个动画都有明确的目的
3. **不过度** - 避免过多或过长的动画
4. **可控制** - 用户可以调整或关闭动画

### 缓动函数

```css
/* 标准缓动 */
--ease-linear: linear;
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* 自定义缓动 */
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
--ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
--ease-back: cubic-bezier(0.175, 0.885, 0.32, 1.275);
```

### 动画时长

```css
/* 动画持续时间 */
--duration-fast: 150ms;
--duration-normal: 300ms;
--duration-slow: 500ms;
--duration-slower: 1000ms;
```

### 宠物动画

#### 基础动画
- **待机动画**: 轻微的呼吸效果，偶尔眨眼
- **行走动画**: 自然的步伐，身体轻微摆动
- **跑步动画**: 快速的腿部运动，身体前倾
- **跳跃动画**: 蓄力、起跳、落地的完整过程

#### 表情动画
- **开心**: 眼睛弯成月牙，嘴角上扬
- **难过**: 眼睛下垂，嘴角下弯
- **生气**: 眉毛皱起，嘴巴噘起
- **困倦**: 眼睛半闭，打哈欠

#### 交互动画
- **被点击**: 轻微缩放和弹跳
- **被拖拽**: 身体略微拉伸
- **喂食**: 咀嚼动作和满足表情
- **清洁**: 舔爪子或摇摆身体

## 图标设计

### 图标风格
- **线条风格**: 2px 粗细的圆润线条
- **填充风格**: 实心图标，圆润边角
- **双色风格**: 主色 + 中性色组合

### 图标尺寸
```css
--icon-xs: 12px;
--icon-sm: 16px;
--icon-base: 20px;
--icon-lg: 24px;
--icon-xl: 32px;
--icon-2xl: 48px;
```

### 常用图标

#### 功能图标
- 🏠 主页
- ⚙️ 设置
- 📊 统计
- 🔔 通知
- ❓ 帮助
- ❌ 关闭
- ✅ 确认
- 🔄 刷新

#### 宠物相关图标
- 🍎 食物
- 💧 水
- 🧸 玩具
- 🛁 清洁
- 💤 睡眠
- ❤️ 健康
- 😊 快乐
- ⚡ 能量

## 响应式设计

### 断点系统

```css
/* 屏幕断点 */
--breakpoint-sm: 640px;   /* 小屏幕 */
--breakpoint-md: 768px;   /* 中等屏幕 */
--breakpoint-lg: 1024px;  /* 大屏幕 */
--breakpoint-xl: 1280px;  /* 超大屏幕 */
--breakpoint-2xl: 1536px; /* 超超大屏幕 */
```

### 适配策略

1. **移动优先**: 从小屏幕开始设计
2. **弹性布局**: 使用 Flexbox 和 Grid
3. **相对单位**: 使用 rem、em、%、vw、vh
4. **图片适配**: 提供多种分辨率的图片

## 可访问性设计

### 颜色对比度
- 正常文本: 至少 4.5:1
- 大文本: 至少 3:1
- 图形元素: 至少 3:1

### 键盘导航
- 所有交互元素可通过键盘访问
- 清晰的焦点指示器
- 逻辑的 Tab 顺序

### 屏幕阅读器支持
- 语义化的 HTML 结构
- 适当的 ARIA 标签
- 有意义的替代文本

## 主题系统

### 明亮主题
```css
.theme-light {
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --text-primary: #212529;
  --text-secondary: #6C757D;
  --border-color: #E9ECEF;
}
```

### 暗黑主题
```css
.theme-dark {
  --bg-primary: #1A1A1A;
  --bg-secondary: #2D2D2D;
  --text-primary: #FFFFFF;
  --text-secondary: #B0B0B0;
  --border-color: #404040;
}
```

### 自动主题
根据系统设置自动切换明亮/暗黑主题。

## 设计资源

### 设计文件
- Figma 设计稿: [链接]
- Sketch 文件: [链接]
- Adobe XD 文件: [链接]

### 资源库
- 图标库: [链接]
- 插画库: [链接]
- 动画库: [链接]
- 字体文件: [链接]

### 设计工具
- **Figma**: 主要设计工具
- **Adobe Illustrator**: 图标和插画
- **Adobe After Effects**: 动画制作
- **Lottie**: 动画导出

---

这个设计系统确保了桌面宠物应用的视觉一致性和用户体验质量。所有设计决策都围绕着创造一个可爱、友好且功能完善的桌面伴侣。
