import React from 'react';
import { createRoot } from 'react-dom/client';
import { App } from './App';
import { ErrorBoundary } from './components/Common/ErrorBoundary';
import { logger } from '@shared/utils/logger';
import './styles/globals.css';

// 全局错误处理
window.addEventListener('error', (event) => {
  logger.error('Global error:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  });
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled promise rejection:', {
    reason: event.reason,
    promise: event.promise
  });
});

// 性能监控
if (process.env.NODE_ENV === 'development') {
  // 监控渲染性能
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'measure') {
        logger.debug('Performance measure:', {
          name: entry.name,
          duration: entry.duration
        });
      }
    }
  });
  
  observer.observe({ entryTypes: ['measure'] });
}

// 初始化应用
const initializeApp = async () => {
  try {
    logger.info('Initializing Desktop Pet renderer...');

    // 获取根元素
    const rootElement = document.getElementById('root');
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // 创建React根
    const root = createRoot(rootElement);

    // 渲染应用
    root.render(
      <React.StrictMode>
        <ErrorBoundary>
          <App />
        </ErrorBoundary>
      </React.StrictMode>
    );

    logger.info('Desktop Pet renderer initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize renderer:', error);
    
    // 显示错误信息
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.innerHTML = `
        <div class="error">
          <div class="error-icon">⚠️</div>
          <div class="error-message">
            应用初始化失败<br>
            请重启应用或联系技术支持
          </div>
        </div>
      `;
    }
  }
};

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// 开发模式下的热重载支持
if (process.env.NODE_ENV === 'development' && import.meta.hot) {
  import.meta.hot.accept('./App', () => {
    logger.info('Hot reloading App component...');
  });
}
