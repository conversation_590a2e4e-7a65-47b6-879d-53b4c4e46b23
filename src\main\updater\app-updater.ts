import { autoUpdater } from 'electron-updater';
import { BrowserWindow, dialog } from 'electron';
import { logger } from '@shared/utils/logger';

export class AppUpdater {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.setupAutoUpdater();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupAutoUpdater(): void {
    // 配置更新服务器
    if (process.env.NODE_ENV === 'production') {
      autoUpdater.checkForUpdatesAndNotify();
    }

    // 更新事件监听
    autoUpdater.on('checking-for-update', () => {
      logger.info('Checking for update...');
    });

    autoUpdater.on('update-available', (info) => {
      logger.info('Update available:', info);
      this.notifyUpdateAvailable(info);
    });

    autoUpdater.on('update-not-available', (info) => {
      logger.info('Update not available:', info);
    });

    autoUpdater.on('error', (err) => {
      logger.error('Error in auto-updater:', err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      const logMessage = `Download speed: ${progressObj.bytesPerSecond} - Downloaded ${progressObj.percent}% (${progressObj.transferred}/${progressObj.total})`;
      logger.info(logMessage);
      
      // 通知渲染进程更新进度
      if (this.mainWindow) {
        this.mainWindow.webContents.send('update-download-progress', progressObj);
      }
    });

    autoUpdater.on('update-downloaded', (info) => {
      logger.info('Update downloaded:', info);
      this.notifyUpdateDownloaded(info);
    });
  }

  private notifyUpdateAvailable(info: any): void {
    if (!this.mainWindow) return;

    const options = {
      type: 'info' as const,
      title: '更新可用',
      message: `发现新版本 ${info.version}`,
      detail: '是否现在下载更新？',
      buttons: ['现在下载', '稍后提醒', '跳过此版本'],
      defaultId: 0,
      cancelId: 1
    };

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // 开始下载
        autoUpdater.downloadUpdate();
      } else if (result.response === 2) {
        // 跳过此版本
        logger.info('User skipped update version:', info.version);
      }
    });
  }

  private notifyUpdateDownloaded(info: any): void {
    if (!this.mainWindow) return;

    const options = {
      type: 'info' as const,
      title: '更新已下载',
      message: `新版本 ${info.version} 已下载完成`,
      detail: '重启应用以应用更新？',
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1
    };

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // 立即重启并安装更新
        autoUpdater.quitAndInstall();
      }
    });
  }

  checkForUpdates(): void {
    if (process.env.NODE_ENV === 'production') {
      autoUpdater.checkForUpdatesAndNotify();
    } else {
      logger.info('Auto-updater disabled in development mode');
    }
  }

  downloadUpdate(): void {
    autoUpdater.downloadUpdate();
  }

  quitAndInstall(): void {
    autoUpdater.quitAndInstall();
  }
}
