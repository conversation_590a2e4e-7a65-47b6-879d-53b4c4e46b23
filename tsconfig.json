{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@main/*": ["src/main/*"], "@assets/*": ["src/assets/*"]}, "types": ["node", "jest", "@testing-library/jest-dom"]}, "include": ["src/**/*", "tests/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist", "release"], "references": [{"path": "./tsconfig.main.json"}, {"path": "./tsconfig.renderer.json"}]}