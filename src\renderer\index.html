<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;" />
  <title>桌面宠物 - Desktop Pet</title>
  <style>
    /* 基础样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: transparent;
      font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      user-select: none;
      -webkit-user-select: none;
      -webkit-app-region: no-drag;
    }

    #root {
      width: 100%;
      height: 100%;
      background: transparent;
      position: relative;
    }

    /* 加载动画 */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: transparent;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 107, 157, 0.3);
      border-top: 3px solid #FF6B9D;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 错误状态 */
    .error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 20px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      backdrop-filter: blur(10px);
    }

    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #DC3545;
    }

    .error-message {
      font-size: 14px;
      color: #495057;
      text-align: center;
      line-height: 1.5;
    }

    /* 隐藏滚动条 */
    ::-webkit-scrollbar {
      display: none;
    }

    /* 禁用文本选择 */
    ::selection {
      background: transparent;
    }

    ::-moz-selection {
      background: transparent;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="loading">
      <div class="loading-spinner"></div>
    </div>
  </div>

  <script type="module" src="./main.tsx"></script>
</body>
</html>
