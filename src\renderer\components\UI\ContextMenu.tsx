import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useAppStore } from '../../stores/appStore';
import { clsx } from 'clsx';

interface ContextMenuProps {
  x: number;
  y: number;
  onClose: () => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, onClose }) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const { currentPet, feedPet, playWithPet, cleanPet } = usePetStore();
  const { setSettingsOpen, quitApp } = useAppStore();

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const menuItems: MenuItem[] = [
    {
      id: 'feed',
      label: '喂食',
      icon: '🍎',
      action: () => {
        feedPet();
        onClose();
      },
      disabled: !currentPet || currentPet.stats.hunger > 90
    },
    {
      id: 'play',
      label: '玩耍',
      icon: '🎾',
      action: () => {
        playWithPet();
        onClose();
      },
      disabled: !currentPet || currentPet.stats.energy < 20
    },
    {
      id: 'clean',
      label: '清洁',
      icon: '🛁',
      action: () => {
        cleanPet();
        onClose();
      },
      disabled: !currentPet || currentPet.stats.cleanliness > 90
    },
    {
      id: 'separator1',
      label: '',
      icon: '',
      action: () => {},
      separator: true
    },
    {
      id: 'status',
      label: '查看状态',
      icon: '📊',
      action: () => {
        // TODO: 显示详细状态
        onClose();
      }
    },
    {
      id: 'settings',
      label: '设置',
      icon: '⚙️',
      action: () => {
        setSettingsOpen(true);
        onClose();
      }
    },
    {
      id: 'separator2',
      label: '',
      icon: '',
      action: () => {},
      separator: true
    },
    {
      id: 'hide',
      label: '隐藏宠物',
      icon: '👁️',
      action: () => {
        window.electronAPI?.hideWindow();
        onClose();
      }
    },
    {
      id: 'quit',
      label: '退出',
      icon: '❌',
      action: () => {
        quitApp();
        onClose();
      }
    }
  ];

  // 计算菜单位置，确保不超出屏幕边界
  const getMenuPosition = () => {
    const menuWidth = 200;
    const menuHeight = menuItems.filter(item => !item.separator).length * 40 + 
                      menuItems.filter(item => item.separator).length * 10 + 16;
    
    let adjustedX = x;
    let adjustedY = y;

    // 检查右边界
    if (x + menuWidth > window.innerWidth) {
      adjustedX = window.innerWidth - menuWidth - 10;
    }

    // 检查下边界
    if (y + menuHeight > window.innerHeight) {
      adjustedY = window.innerHeight - menuHeight - 10;
    }

    // 确保不超出左上边界
    adjustedX = Math.max(10, adjustedX);
    adjustedY = Math.max(10, adjustedY);

    return { x: adjustedX, y: adjustedY };
  };

  const position = getMenuPosition();

  return (
    <motion.div
      ref={menuRef}
      className="fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-[200px]"
      style={{
        left: position.x,
        top: position.y,
      }}
      initial={{ opacity: 0, scale: 0.95, y: -10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: -10 }}
      transition={{ duration: 0.15, ease: "easeOut" }}
    >
      {menuItems.map((item) => {
        if (item.separator) {
          return (
            <div
              key={item.id}
              className="h-px bg-gray-200 mx-2 my-1"
            />
          );
        }

        return (
          <motion.button
            key={item.id}
            className={clsx(
              "w-full px-4 py-2 text-left flex items-center space-x-3",
              "hover:bg-gray-50 transition-colors duration-150",
              "text-sm text-gray-700",
              item.disabled && "opacity-50 cursor-not-allowed"
            )}
            onClick={item.action}
            disabled={item.disabled}
            whileHover={!item.disabled ? { backgroundColor: "#F9FAFB" } : {}}
            whileTap={!item.disabled ? { scale: 0.98 } : {}}
          >
            <span className="text-base">{item.icon}</span>
            <span className="flex-1">{item.label}</span>
            
            {/* 快捷键提示 */}
            {item.id === 'settings' && (
              <span className="text-xs text-gray-400">Ctrl+S</span>
            )}
            {item.id === 'hide' && (
              <span className="text-xs text-gray-400">Ctrl+H</span>
            )}
          </motion.button>
        );
      })}

      {/* 宠物状态快速预览 */}
      {currentPet && (
        <>
          <div className="h-px bg-gray-200 mx-2 my-1" />
          <div className="px-4 py-2">
            <div className="text-xs text-gray-500 mb-1">宠物状态</div>
            <div className="grid grid-cols-2 gap-1 text-xs">
              <div className="flex items-center space-x-1">
                <span>🍎</span>
                <span className={clsx(
                  currentPet.stats.hunger < 30 ? 'text-red-500' :
                  currentPet.stats.hunger < 60 ? 'text-yellow-500' :
                  'text-green-500'
                )}>
                  {Math.round(currentPet.stats.hunger)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span>😊</span>
                <span className={clsx(
                  currentPet.stats.happiness < 30 ? 'text-red-500' :
                  currentPet.stats.happiness < 60 ? 'text-yellow-500' :
                  'text-green-500'
                )}>
                  {Math.round(currentPet.stats.happiness)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span>❤️</span>
                <span className={clsx(
                  currentPet.stats.health < 30 ? 'text-red-500' :
                  currentPet.stats.health < 60 ? 'text-yellow-500' :
                  'text-green-500'
                )}>
                  {Math.round(currentPet.stats.health)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span>⚡</span>
                <span className={clsx(
                  currentPet.stats.energy < 30 ? 'text-red-500' :
                  currentPet.stats.energy < 60 ? 'text-yellow-500' :
                  'text-green-500'
                )}>
                  {Math.round(currentPet.stats.energy)}%
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </motion.div>
  );
};
