export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  data?: any;
  source?: string;
}

class Logger {
  private logLevel: LogLevel = LogLevel.INFO;
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;

  constructor() {
    // 在开发模式下启用调试日志
    if (process.env.NODE_ENV === 'development') {
      this.logLevel = LogLevel.DEBUG;
    }
  }

  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatMessage(level: LogLevel, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const dataStr = data ? ` ${JSON.stringify(data)}` : '';
    return `[${timestamp}] [${levelName}] ${message}${dataStr}`;
  }

  private addLog(level: LogLevel, message: string, data?: any): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data,
      source: this.getCallerInfo()
    };

    this.logs.push(logEntry);

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private getCallerInfo(): string {
    const stack = new Error().stack;
    if (!stack) return 'unknown';

    const lines = stack.split('\n');
    // 跳过当前函数和调用的日志函数
    const callerLine = lines[4] || lines[3] || lines[2];
    
    if (!callerLine) return 'unknown';

    // 提取文件名和行号
    const match = callerLine.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/);
    if (match) {
      const [, functionName, filePath, lineNumber] = match;
      const fileName = filePath.split(/[/\\]/).pop() || filePath;
      return `${fileName}:${lineNumber}`;
    }

    return 'unknown';
  }

  debug(message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    this.addLog(LogLevel.DEBUG, message, data);
    console.debug(this.formatMessage(LogLevel.DEBUG, message, data));
  }

  info(message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    this.addLog(LogLevel.INFO, message, data);
    console.info(this.formatMessage(LogLevel.INFO, message, data));
  }

  warn(message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    this.addLog(LogLevel.WARN, message, data);
    console.warn(this.formatMessage(LogLevel.WARN, message, data));
  }

  error(message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    this.addLog(LogLevel.ERROR, message, data);
    console.error(this.formatMessage(LogLevel.ERROR, message, data));
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  exportLogs(): string {
    return this.logs
      .map(log => this.formatMessage(log.level, log.message, log.data))
      .join('\n');
  }

  // 性能测量
  time(label: string): void {
    console.time(label);
  }

  timeEnd(label: string): void {
    console.timeEnd(label);
  }

  // 分组日志
  group(label: string): void {
    console.group(label);
  }

  groupEnd(): void {
    console.groupEnd();
  }

  // 表格日志
  table(data: any): void {
    console.table(data);
  }

  // 断言日志
  assert(condition: boolean, message: string, data?: any): void {
    if (!condition) {
      this.error(`Assertion failed: ${message}`, data);
      console.assert(condition, message, data);
    }
  }

  // 计数日志
  count(label: string = 'default'): void {
    console.count(label);
  }

  countReset(label: string = 'default'): void {
    console.countReset(label);
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 导出日志级别枚举
export { LogLevel };

// 错误处理工具
export class ErrorLogger {
  static logError(error: Error, context?: string): void {
    const errorInfo = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      context
    };

    logger.error('Unhandled error occurred', errorInfo);

    // 在生产环境中，可以发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送到错误监控服务
      // ErrorReporter.report(error, context);
    }
  }

  static logPromiseRejection(reason: any, promise: Promise<any>): void {
    const rejectionInfo = {
      reason: reason?.toString() || 'Unknown reason',
      stack: reason?.stack,
      promise: promise.toString()
    };

    logger.error('Unhandled promise rejection', rejectionInfo);

    // 在生产环境中，可以发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // ErrorReporter.report(new Error('Unhandled Promise Rejection'), rejectionInfo);
    }
  }
}

// 性能监控工具
export class PerformanceLogger {
  private static timers: Map<string, number> = new Map();

  static startTimer(label: string): void {
    this.timers.set(label, performance.now());
    logger.debug(`Timer started: ${label}`);
  }

  static endTimer(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      logger.warn(`Timer not found: ${label}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(label);
    
    logger.debug(`Timer ended: ${label}`, { duration: `${duration.toFixed(2)}ms` });
    return duration;
  }

  static measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(label);
    return fn().finally(() => {
      this.endTimer(label);
    });
  }

  static measure<T>(label: string, fn: () => T): T {
    this.startTimer(label);
    try {
      return fn();
    } finally {
      this.endTimer(label);
    }
  }
}
