# 应用配置
VITE_APP_NAME=Desktop Pet
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=A cute desktop companion application

# 开发配置
VITE_DEV_MODE=true
VITE_DEBUG_MODE=true
VITE_HOT_RELOAD=true

# API 配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=5000

# 功能开关
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CRASH_REPORTING=false
VITE_ENABLE_AUTO_UPDATE=true

# 第三方服务
VITE_SENTRY_DSN=
VITE_ANALYTICS_ID=

# 构建配置
VITE_BUILD_TARGET=electron
VITE_OUTPUT_DIR=dist
