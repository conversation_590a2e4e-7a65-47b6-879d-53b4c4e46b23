{"name": "desktop-pet", "version": "1.0.0", "description": "A cute desktop companion application", "main": "dist/main/main.js", "homepage": "https://desktop-pet.example.com", "author": {"name": "Desktop Pet Team", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "cross-env NODE_ENV=development electron-forge start", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:dev": "cross-env NODE_ENV=development npm run build", "build:analyze": "npm run build:renderer -- --analyze", "start": "electron dist/main/main.js", "pack": "electron-builder", "pack:win": "electron-builder --win", "pack:mac": "electron-builder --mac", "pack:linux": "electron-builder --linux", "pack:all": "electron-builder --win --mac --linux", "dist": "npm run build && npm run pack:all", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "clean": "rimraf dist release", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "create:component": "node scripts/create-component.js"}, "dependencies": {"electron-updater": "^6.1.7", "react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.16", "zustand": "^4.4.7", "date-fns": "^2.30.0", "lodash": "^4.17.21", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.5", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.10", "typescript": "^5.3.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "electron-forge": "^7.2.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "playwright": "^1.40.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "rimraf": "^5.0.5", "conventional-changelog-cli": "^4.1.0"}, "keywords": ["desktop", "pet", "companion", "electron", "react", "typescript", "cute", "animation"], "repository": {"type": "git", "url": "https://github.com/your-username/desktop-pet.git"}, "bugs": {"url": "https://github.com/your-username/desktop-pet/issues"}}