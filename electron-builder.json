{"appId": "com.example.desktop-pet", "productName": "Desktop Pet", "copyright": "Copyright © 2024 Desktop Pet Team", "directories": {"output": "release", "buildResources": "build"}, "files": ["dist/**/*", "src/assets/**/*", "package.json"], "extraResources": [{"from": "src/assets/", "to": "assets/"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "src/assets/icons/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "src/assets/icons/icon.ico", "uninstallerIcon": "src/assets/icons/icon.ico", "installerHeaderIcon": "src/assets/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Desktop Pet"}, "portable": {"artifactName": "${productName}-Portable-${version}.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "src/assets/icons/icon.icns", "category": "public.app-category.entertainment", "artifactName": "${productName}-${version}-${arch}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "dmg": {"title": "${productName} ${version}", "icon": "src/assets/icons/icon.icns", "background": "build/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "src/assets/icons/icon.png", "category": "Game", "artifactName": "${productName}-${version}-${arch}.${ext}", "synopsis": "A cute desktop companion application", "description": "Desktop Pet is a cute desktop companion that brings joy to your daily work and life."}, "deb": {"depends": ["libgtk-3-0", "libnotify4", "libnss3", "libxss1", "libxtst6", "xdg-utils", "libatspi2.0-0", "libdrm2", "libxcomposite1", "libxdamage1", "libxrandr2", "libgbm1", "libxkbcommon0", "libasound2"]}, "rpm": {"depends": ["gtk3", "libnotify", "nss", "libXScrnSaver", "libXtst", "xdg-utils", "at-spi2-atk", "libdrm", "libXcomposite", "libXdamage", "libXrandr", "mesa-libgbm", "libxkbcommon", "alsa-lib"]}, "publish": [{"provider": "github", "owner": "your-username", "repo": "desktop-pet"}], "buildVersion": "1.0.0", "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false}